/* Base Styles */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-sans);
  font-size: var(--font-base);
  line-height: var(--line-height-base);
  color: var(--text);
  background-color: var(--background);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin-bottom: var(--spacing-base);
  font-weight: var(--fw-semibold);
  line-height: var(--line-height-tight);
  color: var(--text);
}

h1 {
  font-size: var(--font-4xl);
}

h2 {
  font-size: var(--font-3xl);
}

h3 {
  font-size: var(--font-2xl);
}

h4 {
  font-size: var(--font-xl);
}

h5 {
  font-size: var(--font-lg);
}

h6 {
  font-size: var(--font-md);
}

p {
  margin-bottom: var(--spacing-base);
}

a {
  color: var(--primary);
  text-decoration: none;
  transition: var(--transition-base);
}

a:hover {
  color: var(--primary-dark);
}

ul, ol {
  margin-bottom: var(--spacing-base);
  padding-left: var(--spacing-xl);
}

/* Container */
.container {
  width: 100%;
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--spacing-base);
}

/* Header and Navigation styles moved to navigation.css for consistency */

/* User Actions and Mobile Menu styles moved to navigation.css for consistency */

/* Main Content */
.main-content {
  flex: 1;
  padding: var(--spacing-2xl) 0;
}

/* Footer */
.main-footer {
  background-color: var(--dark);
  color: var(--white);
  padding: var(--spacing-4xl) 0 var(--spacing-2xl);
  margin-top: auto;
}

.footer-content {
  display: grid;
  grid-template-columns: var(--grid-cols-4);
  gap: var(--gap-2xl);
}

.footer-logo {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.footer-logo img {
  height: 40px;
}

.footer-section h4 {
  color: var(--white);
  margin-bottom: var(--spacing-lg);
  font-size: var(--font-lg);
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: var(--spacing-sm);
}

.footer-section a {
  color: var(--gray-300);
  transition: var(--transition-base);
}

.footer-section a:hover {
  color: var(--white);
}

.footer-bottom {
  margin-top: var(--spacing-4xl);
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--gray-700);
  text-align: center;
  color: var(--gray-400);
  font-size: var(--font-sm);
}
