{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}Manage Availability - PetPaw{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            {% include 'components/provider_sidebar.html' %}
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Availability Header -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Manage Availability</h5>
                    <button type="button" class="btn btn-primary" onclick="openAvailabilityModal()">
                        <i class="fas fa-plus-circle"></i> Add Availability
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Day</th>
                                            <th>Morning (6AM-12PM)</th>
                                            <th>Afternoon (12PM-5PM)</th>
                                            <th>Evening (5PM-10PM)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for day_name, day_slots in availability_by_day.items %}
                                        <tr>
                                            <td class="fw-bold">{{ day_name }}</td>
                                            <td>
                                                {% for slot in day_slots.morning %}
                                                <div class="availability-slot">
                                                    <span class="slot-time">{{ slot.start_time|time:"g:i A" }} - {{ slot.end_time|time:"g:i A" }}</span>
                                                    <form action="{% url 'delete-availability' slot.id %}" method="post" class="slot-delete-form">
                                                        {% csrf_token %}
                                                        <button type="submit" class="btn-delete" onclick="return confirm('Are you sure you want to delete this availability slot?')" title="Delete availability">
                                                            <i class="fas fa-trash-alt"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                                {% empty %}
                                                <p class="text-muted small mb-0">No availability</p>
                                                {% endfor %}
                                            </td>
                                            <td>
                                                {% for slot in day_slots.afternoon %}
                                                <div class="availability-slot">
                                                    <span class="slot-time">{{ slot.start_time|time:"g:i A" }} - {{ slot.end_time|time:"g:i A" }}</span>
                                                    <form action="{% url 'delete-availability' slot.id %}" method="post" class="slot-delete-form">
                                                        {% csrf_token %}
                                                        <button type="submit" class="btn-delete" onclick="return confirm('Are you sure you want to delete this availability slot?')" title="Delete availability">
                                                            <i class="fas fa-trash-alt"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                                {% empty %}
                                                <p class="text-muted small mb-0">No availability</p>
                                                {% endfor %}
                                            </td>
                                            <td>
                                                {% for slot in day_slots.evening %}
                                                <div class="availability-slot">
                                                    <span class="slot-time">{{ slot.start_time|time:"g:i A" }} - {{ slot.end_time|time:"g:i A" }}</span>
                                                    <form action="{% url 'delete-availability' slot.id %}" method="post" class="slot-delete-form">
                                                        {% csrf_token %}
                                                        <button type="submit" class="btn-delete" onclick="return confirm('Are you sure you want to delete this availability slot?')" title="Delete availability">
                                                            <i class="fas fa-trash-alt"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                                {% empty %}
                                                <p class="text-muted small mb-0">No availability</p>
                                                {% endfor %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Availability Tips -->
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Availability Tips</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">
                                    <i class="fas fa-info-circle text-primary me-2"></i>
                                    Set your availability for each day of the week to let clients know when you can provide services.
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-info-circle text-primary me-2"></i>
                                    You can add multiple time slots for each day to accommodate different schedules.
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-info-circle text-primary me-2"></i>
                                    Clients can only book appointments during your available time slots.
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-info-circle text-primary me-2"></i>
                                    Remember to keep your availability up to date to avoid scheduling conflicts.
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Availability Modal -->
<div id="availabilityModal" class="modal-overlay" style="display: none;">
    <div class="modal-container">
        <div class="modal-header">
            <h3 class="modal-title">Add Availability</h3>
            <button type="button" class="modal-close" onclick="closeAvailabilityModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form method="post">
            {% csrf_token %}
            <div class="modal-body">
                <div class="form-group">
                    <label for="{{ availability_form.day_of_week.id_for_label }}">Day of Week:</label>
                    {{ availability_form.day_of_week }}
                </div>
                <div class="form-group">
                    <label for="{{ availability_form.start_time.id_for_label }}">Start Time:</label>
                    {{ availability_form.start_time }}
                </div>
                <div class="form-group">
                    <label for="{{ availability_form.end_time.id_for_label }}">End Time:</label>
                    {{ availability_form.end_time }}
                </div>
                {% if availability_form.errors %}
                    <div class="alert alert-danger">
                        {{ availability_form.errors }}
                    </div>
                {% endif %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeAvailabilityModal()">Cancel</button>
                <button type="submit" class="btn btn-primary">Add Availability</button>
            </div>
        </form>
    </div>
</div>

<script>
function openAvailabilityModal() {
    document.getElementById('availabilityModal').style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

function closeAvailabilityModal() {
    document.getElementById('availabilityModal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Close modal when clicking outside
document.getElementById('availabilityModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeAvailabilityModal();
    }
});

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeAvailabilityModal();
    }
});
</script>
{% endblock %}
