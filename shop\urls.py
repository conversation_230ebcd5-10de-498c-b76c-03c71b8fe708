from django.urls import path
from . import views

urlpatterns = [
    # Product and shopping URLs
    path('', views.ProductListView.as_view(), name='product-list'),
    path('product/<int:pk>/', views.ProductDetailView.as_view(), name='product-detail'),
    path('product/<int:pk>/review/', views.add_review, name='add-review'),
    path('product/<int:pk>/add-to-cart/', views.add_to_cart, name='add-to-cart'),
    path('cart/', views.view_cart, name='cart'),
    path('cart/update/<int:pk>/', views.update_cart_item, name='update-cart-item'),
    path('cart/remove/<int:pk>/', views.remove_from_cart, name='remove-from-cart'),
    path('checkout/', views.CheckoutView.as_view(), name='checkout'),
    path('orders/', views.order_list, name='order-list'),
    path('orders/<int:pk>/', views.order_detail, name='order-detail'),

    # Seller URLs
    path('become-seller/', views.become_seller, name='become-seller'),
    path('seller/dashboard/', views.seller_dashboard, name='seller-dashboard'),
    path('seller/products/', views.seller_products, name='seller-products'),
    path('seller/products/add/', views.add_product, name='add-product'),
    path('seller/products/<int:pk>/edit/', views.edit_product, name='edit-product'),
    path('seller/products/<int:pk>/delete/', views.delete_product, name='delete-product'),
    path('seller/orders/', views.seller_orders, name='seller-orders'),
    path('seller/settings/', views.seller_settings, name='seller-settings'),
]
