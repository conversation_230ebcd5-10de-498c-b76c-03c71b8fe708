/**
 * Unified Tab System for PetPaw
 * Provides consistent tab functionality across all pages
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeTabs();
});

function initializeTabs() {
    // Initialize all tab containers on the page
    const tabContainers = document.querySelectorAll('.tabs-container');
    
    tabContainers.forEach(container => {
        const tabLinks = container.querySelectorAll('.tab-link');
        const tabPanes = container.parentElement.querySelectorAll('.tab-pane');
        
        // If no tab panes found in parent, look for them in the container itself
        const actualTabPanes = tabPanes.length > 0 ? tabPanes : container.querySelectorAll('.tab-pane');
        
        tabLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Remove active class from all tabs in this container
                tabLinks.forEach(tab => tab.classList.remove('active'));
                actualTabPanes.forEach(pane => pane.classList.remove('active'));
                
                // Add active class to clicked tab
                this.classList.add('active');
                
                // Show corresponding tab content
                const target = this.getAttribute('href').substring(1);
                const targetPane = document.getElementById(target);
                if (targetPane) {
                    targetPane.classList.add('active');
                }
                
                // Update URL hash for bookmarking
                if (history.replaceState) {
                    history.replaceState(null, null, '#' + target);
                }
            });
        });
        
        // Handle initial hash in URL
        if (window.location.hash) {
            const hashTarget = window.location.hash.substring(1);
            const hashLink = container.querySelector(`[href="#${hashTarget}"]`);
            if (hashLink) {
                hashLink.click();
            }
        }
    });
}

/**
 * Programmatically activate a tab
 * @param {string} tabId - The ID of the tab to activate
 * @param {string} containerId - Optional: ID of the specific tab container
 */
function activateTab(tabId, containerId = null) {
    let container;
    if (containerId) {
        container = document.getElementById(containerId);
    } else {
        // Find the container that has a link to this tab
        container = document.querySelector(`[href="#${tabId}"]`)?.closest('.tabs-container');
    }
    
    if (!container) return false;
    
    const tabLink = container.querySelector(`[href="#${tabId}"]`);
    if (tabLink) {
        tabLink.click();
        return true;
    }
    
    return false;
}

/**
 * Get the currently active tab in a container
 * @param {string} containerId - ID of the tab container
 * @returns {string|null} - ID of the active tab or null
 */
function getActiveTab(containerId) {
    const container = document.getElementById(containerId);
    if (!container) return null;
    
    const activeLink = container.querySelector('.tab-link.active');
    if (activeLink) {
        return activeLink.getAttribute('href').substring(1);
    }
    
    return null;
}

/**
 * Add a new tab dynamically
 * @param {string} containerId - ID of the tab container
 * @param {Object} tabConfig - Configuration for the new tab
 */
function addTab(containerId, tabConfig) {
    const container = document.getElementById(containerId);
    if (!container) return false;
    
    const tabList = container.querySelector('.tab-list');
    const tabContent = container.parentElement.querySelector('.tab-content');
    
    if (!tabList || !tabContent) return false;
    
    // Create tab item
    const tabItem = document.createElement('li');
    tabItem.className = 'tab-item';
    
    const tabLink = document.createElement('a');
    tabLink.href = `#${tabConfig.id}`;
    tabLink.className = 'tab-link';
    tabLink.innerHTML = tabConfig.icon ? `<i class="${tabConfig.icon}"></i> ${tabConfig.title}` : tabConfig.title;
    
    tabItem.appendChild(tabLink);
    tabList.appendChild(tabItem);
    
    // Create tab pane
    const tabPane = document.createElement('div');
    tabPane.id = tabConfig.id;
    tabPane.className = 'tab-pane';
    tabPane.innerHTML = tabConfig.content || '';
    
    tabContent.appendChild(tabPane);
    
    // Re-initialize tabs to include the new one
    initializeTabs();
    
    return true;
}

/**
 * Remove a tab dynamically
 * @param {string} tabId - ID of the tab to remove
 */
function removeTab(tabId) {
    const tabLink = document.querySelector(`[href="#${tabId}"]`);
    const tabPane = document.getElementById(tabId);
    
    if (tabLink) {
        const tabItem = tabLink.closest('.tab-item');
        if (tabItem) tabItem.remove();
    }
    
    if (tabPane) {
        tabPane.remove();
    }
    
    return true;
}

// Export functions for global use
window.TabSystem = {
    initialize: initializeTabs,
    activate: activateTab,
    getActive: getActiveTab,
    add: addTab,
    remove: removeTab
};
