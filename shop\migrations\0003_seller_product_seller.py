# Generated by Django 4.2.7 on 2025-05-28 21:02

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('shop', '0002_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Seller',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('business_name', models.CharField(help_text='Name of your business/store', max_length=200)),
                ('business_description', models.TextField(help_text='Describe your business and what you sell')),
                ('business_address', models.TextField(help_text='Physical address of your business')),
                ('business_phone', models.CharField(help_text='Business contact phone number', max_length=15)),
                ('business_email', models.EmailField(help_text='Business contact email', max_length=254)),
                ('business_website', models.URLField(blank=True, help_text='Your business website (optional)')),
                ('business_logo', models.ImageField(blank=True, null=True, upload_to='seller_logos')),
                ('is_verified', models.BooleanField(default=False, help_text='Whether the seller is verified')),
                ('is_active', models.BooleanField(default=True, help_text='Whether the seller can list products')),
                ('rating', models.DecimalField(decimal_places=2, default=0, max_digits=3)),
                ('reviews_count', models.PositiveIntegerField(default=0)),
                ('total_sales', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='seller', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='product',
            name='seller',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='products', to='shop.seller'),
        ),
    ]
