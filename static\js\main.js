// Main JavaScript file for PetPaw

document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const mobileMenu = document.querySelector('.mobile-menu');
    const mobileMenuClose = document.querySelector('.mobile-menu-close');

    if (mobileMenuToggle && mobileMenu && mobileMenuClose) {
        mobileMenuToggle.addEventListener('click', function() {
            mobileMenu.classList.add('active');
            document.body.style.overflow = 'hidden';

            // Add smooth animation
            mobileMenu.style.transition = 'right 0.3s ease-in-out';
        });

        mobileMenuClose.addEventListener('click', function() {
            mobileMenu.classList.remove('active');
            document.body.style.overflow = '';
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!mobileMenu.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                mobileMenu.classList.remove('active');
                document.body.style.overflow = '';
            }
        });

        // Close mobile menu on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && mobileMenu.classList.contains('active')) {
                mobileMenu.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
    }

    // User dropdown toggle
    const dropdownToggle = document.querySelector('.dropdown-toggle');
    const dropdownMenu = document.querySelector('.dropdown-menu');

    if (dropdownToggle && dropdownMenu) {
        dropdownToggle.addEventListener('click', function(e) {
            e.stopPropagation();
            dropdownMenu.classList.toggle('show');
        });

        document.addEventListener('click', function(e) {
            if (!dropdownToggle.contains(e.target) && !dropdownMenu.contains(e.target)) {
                dropdownMenu.classList.remove('show');
            }
        });
    }

    // Django message close button
    const messageCloseButtons = document.querySelectorAll('.django-message-close');

    if (messageCloseButtons.length > 0) {
        messageCloseButtons.forEach(button => {
            button.addEventListener('click', function() {
                const message = this.closest('.django-message');
                message.style.opacity = '0';
                setTimeout(() => {
                    message.remove();
                }, 300);
            });
        });

        // Auto-hide Django messages after 3 seconds, but immediately hide notification-related ones
        setTimeout(() => {
            document.querySelectorAll('.django-message').forEach(message => {
                const messageText = message.textContent || '';

                // Immediately hide notification-related messages
                if (messageText.includes('Message from') ||
                    messageText.includes('Conversation') ||
                    messageText.includes('sent you a message') ||
                    messageText.includes('interested in') ||
                    messageText.includes('responded to your inquiry')) {
                    message.remove();
                } else {
                    // Normal fade out for other messages
                    message.style.opacity = '0';
                    setTimeout(() => {
                        message.remove();
                    }, 300);
                }
            });
        }, 3000);

        // Immediately hide notification-related messages on page load
        document.querySelectorAll('.django-message').forEach(message => {
            const messageText = message.textContent || '';

            if (messageText.includes('Message from') ||
                messageText.includes('Conversation') ||
                messageText.includes('sent you a message') ||
                messageText.includes('interested in') ||
                messageText.includes('responded to your inquiry')) {
                message.style.display = 'none';
                setTimeout(() => {
                    message.remove();
                }, 100);
            }
        });
    }

    // Product quantity increment/decrement
    const quantityInputs = document.querySelectorAll('.quantity-input');

    if (quantityInputs.length > 0) {
        quantityInputs.forEach(input => {
            const decrementBtn = input.previousElementSibling;
            const incrementBtn = input.nextElementSibling;

            decrementBtn.addEventListener('click', function() {
                let value = parseInt(input.value);
                if (value > 1) {
                    input.value = value - 1;

                    // Trigger change event for any listeners
                    const event = new Event('change', { bubbles: true });
                    input.dispatchEvent(event);
                }
            });

            incrementBtn.addEventListener('click', function() {
                let value = parseInt(input.value);
                input.value = value + 1;

                // Trigger change event for any listeners
                const event = new Event('change', { bubbles: true });
                input.dispatchEvent(event);
            });
        });
    }

    // Cart item quantity update
    const cartQuantityForms = document.querySelectorAll('.cart-item-quantity-form');

    if (cartQuantityForms.length > 0) {
        cartQuantityForms.forEach(form => {
            const input = form.querySelector('.quantity-input');

            input.addEventListener('change', function() {
                form.submit();
            });
        });
    }

    // Add to cart AJAX
    const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');

    if (addToCartButtons.length > 0) {
        addToCartButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                const url = this.getAttribute('data-url');

                fetch(url, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRFToken': getCookie('csrftoken')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update cart count
                        const cartCount = document.querySelector('.cart-count');
                        if (cartCount) {
                            cartCount.textContent = data.cart_total;
                        }

                        // Show success message
                        showMessage('Product added to cart successfully!', 'success');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showMessage('Failed to add product to cart.', 'error');
                });
            });
        });
    }

    // Helper function to get CSRF token
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // Enhanced search functionality
    const searchInputs = document.querySelectorAll('.search-input');
    const searchButtons = document.querySelectorAll('.search-button');

    if (searchInputs.length > 0) {
        searchInputs.forEach(input => {
            // Add loading state on form submission
            const form = input.closest('form');
            if (form) {
                form.addEventListener('submit', function() {
                    const button = form.querySelector('.search-button');
                    if (button) {
                        button.classList.add('loading');
                        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                    }
                });
            }

            // Clear search functionality
            input.addEventListener('input', function() {
                if (this.value.length === 0) {
                    const clearBtn = this.parentNode.querySelector('.search-clear');
                    if (clearBtn) {
                        clearBtn.style.display = 'none';
                    }
                } else {
                    let clearBtn = this.parentNode.querySelector('.search-clear');
                    if (!clearBtn) {
                        clearBtn = document.createElement('button');
                        clearBtn.type = 'button';
                        clearBtn.className = 'search-clear';
                        clearBtn.innerHTML = '<i class="fas fa-times"></i>';
                        clearBtn.style.cssText = `
                            position: absolute;
                            right: 45px;
                            top: 50%;
                            transform: translateY(-50%);
                            background: none;
                            border: none;
                            color: var(--gray-500);
                            cursor: pointer;
                            padding: 4px;
                            border-radius: 50%;
                            transition: var(--transition-base);
                        `;
                        this.parentNode.appendChild(clearBtn);

                        clearBtn.addEventListener('click', () => {
                            input.value = '';
                            input.focus();
                            clearBtn.style.display = 'none';
                        });
                    }
                    clearBtn.style.display = 'block';
                }
            });
        });
    }

    // Smooth scroll to top functionality
    const scrollToTopBtn = document.createElement('button');
    scrollToTopBtn.innerHTML = '<i class="fas fa-chevron-up"></i>';
    scrollToTopBtn.className = 'scroll-to-top';
    scrollToTopBtn.style.cssText = `
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: var(--primary);
        color: var(--white);
        border: none;
        cursor: pointer;
        opacity: 0;
        visibility: hidden;
        transition: var(--transition-base);
        z-index: var(--z-40);
        box-shadow: var(--shadow-lg);
    `;
    document.body.appendChild(scrollToTopBtn);

    // Show/hide scroll to top button
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            scrollToTopBtn.style.opacity = '1';
            scrollToTopBtn.style.visibility = 'visible';
        } else {
            scrollToTopBtn.style.opacity = '0';
            scrollToTopBtn.style.visibility = 'hidden';
        }
    });

    scrollToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    // Helper function to show messages - DISABLED to prevent popup notifications
    function showMessage(text, type) {
        // Do nothing - we don't want popup messages for messaging system
        console.log('Message notification (disabled):', text, type);
        return;
    }
});
