{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}My Services - PetPaw{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            {% include 'components/provider_sidebar.html' %}
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Services Header -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">My Services</h5>
                    <a href="{% url 'add-service' %}" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-2"></i> Add New Service
                    </a>
                </div>
                <div class="card-body">
                    {% if services %}
                        <div class="row">
                            {% for service in services %}
                                <div class="col-md-6 mb-4">
                                    <div class="card h-100">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-3">
                                                <h5 class="card-title mb-0">{{ service.name }}</h5>
                                                <span class="badge {% if service.is_available %}bg-success{% else %}bg-danger{% endif %}">
                                                    {% if service.is_available %}Available{% else %}Unavailable{% endif %}
                                                </span>
                                            </div>
                                            <h6 class="text-muted">{{ service.category.name }}</h6>
                                            <p class="card-text">{{ service.description|truncatechars:100 }}</p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h5 class="mb-0">${{ service.price }}</h5>
                                                <span class="text-muted">{{ service.duration }} min</span>
                                            </div>
                                        </div>
                                        <div class="card-footer bg-white d-flex justify-content-between">
                                            <a href="{% url 'update-service' service.id %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit me-1"></i> Edit
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ service.id }}">
                                                <i class="fas fa-trash-alt me-1"></i> Delete
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Delete Modal -->
                                <div class="modal fade" id="deleteModal{{ service.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ service.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ service.id }}">Confirm Delete</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p>Are you sure you want to delete the service <strong>{{ service.name }}</strong>?</p>
                                                <p class="text-danger">This action cannot be undone.</p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <form action="{% url 'delete-service' service.id %}" method="post">
                                                    {% csrf_token %}
                                                    <button type="submit" class="btn btn-danger">Delete</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-concierge-bell fa-3x text-muted mb-3"></i>
                            <h5>No Services Added Yet</h5>
                            <p class="text-muted">Start by adding your first service to attract clients.</p>
                            <a href="{% url 'add-service' %}" class="btn btn-primary mt-2">
                                <i class="fas fa-plus-circle me-2"></i> Add New Service
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
