{% extends 'base.html' %}
{% load static %}

{% block title %}Seller Dashboard - PetPaw{% endblock %}

{% block content %}
<div class="container">
    <div class="seller-dashboard">
        <div class="dashboard-sidebar">
            {% include 'components/seller_sidebar.html' %}
        </div>

        <div class="dashboard-main">
            <!-- Dashboard Overview -->
            <div class="dashboard-header">
                <h1>Dashboard Overview</h1>
                <p>Welcome back, {{ seller.business_name }}!</p>
            </div>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ products_count }}</h3>
                        <p>Total Products</p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ active_products_count }}</h3>
                        <p>Active Products</p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ total_orders }}</h3>
                        <p>Total Orders</p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ seller.rating|floatformat:1 }}</h3>
                        <p>Rating</p>
                    </div>
                </div>
            </div>

            <!-- Recent Orders -->
            <div class="dashboard-section">
                <div class="section-header">
                    <h2>Recent Orders</h2>
                    <a href="{% url 'seller-orders' %}" class="btn btn-outline">View All</a>
                </div>

                {% if recent_orders %}
                    <div class="orders-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>Order ID</th>
                                    <th>Product</th>
                                    <th>Customer</th>
                                    <th>Quantity</th>
                                    <th>Total</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order_item in recent_orders %}
                                <tr>
                                    <td>#{{ order_item.order.id }}</td>
                                    <td>{{ order_item.product.name }}</td>
                                    <td>{{ order_item.order.user.full_name|default:order_item.order.user.email }}</td>
                                    <td>{{ order_item.quantity }}</td>
                                    <td>${{ order_item.get_total_price }}</td>
                                    <td>{{ order_item.order.created_at|date:"M d, Y" }}</td>
                                    <td>
                                        <span class="status-badge status-{{ order_item.order.status }}">
                                            {{ order_item.order.get_status_display }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="empty-state">
                        <i class="fas fa-shopping-cart"></i>
                        <h3>No orders yet</h3>
                        <p>Your orders will appear here once customers start purchasing your products.</p>
                    </div>
                {% endif %}
            </div>

            <!-- Quick Actions -->
            <div class="dashboard-section">
                <div class="section-header">
                    <h2>Quick Actions</h2>
                </div>

                <div class="quick-actions">
                    <a href="{% url 'add-product' %}" class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-plus-circle"></i>
                        </div>
                        <div class="action-content">
                            <h3>Add New Product</h3>
                            <p>List a new product in your store</p>
                        </div>
                    </a>

                    <a href="{% url 'seller-products' %}" class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="action-content">
                            <h3>Manage Products</h3>
                            <p>Edit or update your existing products</p>
                        </div>
                    </a>

                    <a href="{% url 'seller-settings' %}" class="action-card">
                        <div class="action-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="action-content">
                            <h3>Store Settings</h3>
                            <p>Update your business information</p>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.seller-dashboard {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: var(--spacing-xl);
    padding: var(--spacing-xl) 0;
}

.dashboard-main {
    min-height: 600px;
}

.dashboard-header {
    margin-bottom: var(--spacing-2xl);
}

.dashboard-header h1 {
    color: var(--primary);
    margin-bottom: var(--spacing-xs);
}

.dashboard-header p {
    color: var(--text-muted);
    font-size: var(--font-lg);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.stat-card {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-base);
    transition: var(--transition-base);
}

.stat-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-light);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary);
    font-size: var(--font-xl);
}

.stat-content h3 {
    font-size: var(--font-2xl);
    font-weight: var(--fw-bold);
    color: var(--text);
    margin: 0;
}

.stat-content p {
    color: var(--text-muted);
    margin: 0;
    font-size: var(--font-sm);
}

.dashboard-section {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.section-header h2 {
    color: var(--text);
    margin: 0;
}

.btn-outline {
    background: transparent;
    border: 1px solid var(--primary);
    color: var(--primary);
    padding: var(--spacing-xs) var(--spacing-base);
    border-radius: var(--border-radius);
    text-decoration: none;
    font-size: var(--font-sm);
    transition: var(--transition-base);
}

.btn-outline:hover {
    background: var(--primary);
    color: var(--white);
}

.orders-table {
    overflow-x: auto;
}

.orders-table table {
    width: 100%;
    border-collapse: collapse;
}

.orders-table th,
.orders-table td {
    padding: var(--spacing-sm);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.orders-table th {
    background: var(--gray-50);
    font-weight: var(--fw-medium);
    color: var(--text);
}

.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: var(--font-xs);
    font-weight: var(--fw-medium);
    text-transform: uppercase;
}

.status-pending {
    background: var(--warning-light);
    color: var(--warning-dark);
}

.status-processing {
    background: var(--info-light);
    color: var(--info-dark);
}

.status-shipped {
    background: var(--primary-light);
    color: var(--primary-dark);
}

.status-delivered {
    background: var(--success-light);
    color: var(--success-dark);
}

.status-cancelled {
    background: var(--danger-light);
    color: var(--danger-dark);
}

.empty-state {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-muted);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: var(--spacing-base);
    opacity: 0.5;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.action-card {
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-base);
    text-decoration: none;
    transition: var(--transition-base);
}

.action-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
    border-color: var(--primary);
}

.action-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-light);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary);
    font-size: var(--font-lg);
}

.action-content h3 {
    color: var(--text);
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-base);
}

.action-content p {
    color: var(--text-muted);
    margin: 0;
    font-size: var(--font-sm);
}

@media (max-width: 992px) {
    .seller-dashboard {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}
