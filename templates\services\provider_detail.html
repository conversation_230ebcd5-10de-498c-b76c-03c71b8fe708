{% extends 'base.html' %}
{% load static %}

{% block title %}{{ provider.user.get_full_name|default:provider.user.username }} - PetPaw{% endblock %}

{% block content %}
<div class="container">
    <div class="provider-detail-container">
        <!-- Provider Header -->
        <div class="provider-header">
            <div class="provider-avatar">
                {% if provider.profile_picture %}
                    <img src="{{ provider.profile_picture.url }}" alt="{{ provider.user.username }}">
                {% else %}
                    <div class="avatar-placeholder">
                        <i class="fas fa-user"></i>
                    </div>
                {% endif %}
            </div>

            <div class="provider-info">
                <h1>{{ provider.user.full_name|default:provider.user.email }}</h1>
                <div class="provider-rating">
                    <div class="stars">
                        {% for i in "12345" %}
                            {% if forloop.counter <= provider.rating %}
                                <i class="fas fa-star"></i>
                            {% else %}
                                <i class="far fa-star"></i>
                            {% endif %}
                        {% endfor %}
                    </div>
                    <span class="rating-text">{{ provider.rating|floatformat:1 }} ({{ reviews.count }} reviews)</span>
                </div>

                <div class="provider-meta">
                    <div class="meta-item">
                        <i class="fas fa-clock"></i>
                        <span>{{ provider.experience_years }} years experience</span>
                    </div>
                    <div class="meta-item">
                        <i class="fa-solid fa-indian-rupee-sign"></i>
                        <span>₹{{ provider.hourly_rate }}/hour</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>{{ provider.location|default:"Location not specified" }}</span>
                    </div>
                </div>

                <div class="availability-status">
                    {% if provider.is_available %}
                        <span class="status-badge available">
                            <i class="fas fa-check-circle"></i>
                            Available
                        </span>
                    {% else %}
                        <span class="status-badge unavailable">
                            <i class="fas fa-times-circle"></i>
                            Unavailable
                        </span>
                    {% endif %}
                </div>

                <!-- Actions based on whether user is the provider or a visitor -->
                <div class="provider-actions">
                    {% if user.is_authenticated and user == provider.user %}
                        <!-- Provider's own profile - show management options -->
                        <div class="provider-management-actions">
                            <a href="{% url 'provider-settings' %}" class="btn btn-secondary btn-lg">
                                <i class="fas fa-edit"></i>
                                Edit Profile
                            </a>
                            <a href="{% url 'add-service' %}" class="btn btn-primary btn-lg">
                                <i class="fas fa-plus"></i>
                                Add Service
                            </a>
                        </div>
                    {% else %}
                        <!-- Visitor viewing provider profile - show booking options -->
                        {% if provider.is_available and services.exists %}
                            {% if services.count == 1 %}
                                <a href="{% url 'book-service' services.first.pk %}" class="btn btn-primary btn-lg">
                                    <i class="fas fa-calendar-plus"></i>
                                    Book {{ services.first.name }}
                                </a>
                            {% else %}
                                <a href="{% url 'book-provider' provider.pk %}" class="btn btn-primary btn-lg">
                                    <i class="fas fa-calendar-plus"></i>
                                    Book Service
                                </a>
                            {% endif %}
                        {% elif provider.is_available and not services.exists %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                This provider hasn't added any services yet. Please check back later.
                            </div>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Service Categories -->
        {% if provider.categories.exists %}
            <div class="provider-categories">
                <h2>
                    <i class="fas fa-tags"></i>
                    Service Categories
                </h2>
                <div class="categories-grid">
                    {% for category in provider.categories.all %}
                        <div class="category-badge">
                            <div class="category-icon">
                                {% if category.icon %}
                                    <img src="{{ category.icon.url }}" alt="{{ category.name }}">
                                {% elif category.icon_class %}
                                    <i class="{{ category.icon_class }}"></i>
                                {% else %}
                                    <i class="fas fa-cog"></i>
                                {% endif %}
                            </div>
                            <div class="category-info">
                                <h3>{{ category.name }}</h3>
                                {% if category.description %}
                                    <p>{{ category.description|truncatewords:15 }}</p>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}

        <!-- Pet Categories -->
        {% if provider.pet_categories.exists %}
            <div class="provider-pet-categories">
                <h2>
                    <i class="fas fa-paw"></i>
                    Pet Types Served
                </h2>
                <div class="pet-categories-list">
                    {% for pet_category in provider.pet_categories.all %}
                        <span class="pet-category-tag">
                            {% if pet_category.icon %}
                                <img src="{{ pet_category.icon.url }}" alt="{{ pet_category.name }}">
                            {% else %}
                                <i class="fas fa-paw"></i>
                            {% endif %}
                            {{ pet_category.name }}
                        </span>
                    {% endfor %}
                </div>
            </div>
        {% endif %}

        <!-- Provider Bio -->
        {% if provider.bio %}
            <div class="provider-bio">
                <h2>About</h2>
                <p>{{ provider.bio }}</p>
            </div>
        {% endif %}

        <!-- Services -->
        <div class="provider-services">
            <div class="services-header">
                <h2>Services</h2>
                {% if user.is_authenticated and user == provider.user %}
                    <a href="{% url 'add-service' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Add Service
                    </a>
                {% endif %}
            </div>

            {% if services %}
                <div class="services-grid">
                    {% for service in services %}
                        <div class="service-card">
                            <div class="service-header">
                                <h3>{{ service.name }}</h3>
                                <span class="service-price">₹{{ service.price }}</span>
                            </div>
                            <p class="service-description">{{ service.description|truncatewords:20 }}</p>
                            <div class="service-duration">
                                <i class="fas fa-clock"></i>
                                <span>{{ service.duration }} minutes</span>
                            </div>
                            <div class="service-actions">
                                {% if user.is_authenticated and user == provider.user %}
                                    <!-- Provider's own services - show management options -->
                                    <div class="service-management-actions">
                                        <a href="{% url 'update-service' service.pk %}" class="btn btn-secondary btn-sm">
                                            <i class="fas fa-edit"></i>
                                            Edit
                                        </a>
                                        <a href="{% url 'delete-service' service.pk %}" class="btn btn-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                            Delete
                                        </a>
                                    </div>
                                {% else %}
                                    <!-- Visitor viewing services - show booking option -->
                                    <a href="{% url 'book-service' service.pk %}" class="btn btn-primary">
                                        <i class="fas fa-calendar-plus"></i>
                                        Book Service
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    {% if user.is_authenticated and user == provider.user %}
                        <div class="empty-state-content">
                            <i class="fas fa-plus-circle"></i>
                            <h3>No services added yet</h3>
                            <p>Start by adding your first service to attract customers.</p>
                            <a href="{% url 'add-service' %}" class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                Add Your First Service
                            </a>
                        </div>
                    {% else %}
                        <p>No services available at the moment.</p>
                    {% endif %}
                </div>
            {% endif %}
        </div>

        <!-- Availability -->
        {% if availability %}
            <div class="provider-availability">
                <h2>Availability</h2>
                <div class="availability-grid">
                    {% regroup availability by day_of_week as day_list %}
                    {% for day in day_list %}
                        <div class="availability-day">
                            <h4>{{ day.grouper|date:"l" }}</h4>
                            <div class="time-slots">
                                {% for slot in day.list %}
                                    <span class="time-slot">
                                        {{ slot.start_time|time:"g:i A" }} - {{ slot.end_time|time:"g:i A" }}
                                    </span>
                                {% endfor %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}

        <!-- Gallery -->
        {% if gallery %}
            <div class="provider-gallery">
                <h2>Gallery</h2>
                <div class="gallery-grid">
                    {% for image in gallery %}
                        <div class="gallery-item">
                            <img src="{{ image.image.url }}" alt="{{ image.caption|default:'Gallery image' }}">
                            {% if image.caption %}
                                <div class="gallery-caption">{{ image.caption }}</div>
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}

        <!-- Reviews -->
        {% if reviews %}
            <div class="provider-reviews">
                <h2>Reviews</h2>
                <div class="reviews-list">
                    {% for review in reviews %}
                        <div class="review-card">
                            <div class="review-header">
                                <div class="reviewer-info">
                                    <strong>{{ review.booking.user.get_full_name|default:review.booking.user.username }}</strong>
                                    <div class="review-rating">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= review.rating %}
                                                <i class="fas fa-star"></i>
                                            {% else %}
                                                <i class="far fa-star"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                                <span class="review-date">{{ review.created_at|date:"M d, Y" }}</span>
                            </div>
                            <p class="review-comment">{{ review.comment }}</p>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}
    </div>
</div>

<style>
.provider-detail-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: var(--spacing-lg);
}

.provider-header {
    display: flex;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-xl);
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
}

.provider-avatar {
    width: 120px;
    height: 120px;
    border-radius: var(--radius-full);
    overflow: hidden;
    background: var(--gray-200);
    flex-shrink: 0;
}

.provider-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-500);
    font-size: 3rem;
}

.provider-info {
    flex: 1;
}

.provider-info h1 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-2xl);
}

.provider-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-base);
}

.stars {
    color: var(--warning);
    font-size: var(--font-lg);
}

.rating-text {
    color: var(--gray-600);
    font-size: var(--font-sm);
}

.provider-meta {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-base);
    margin-bottom: var(--spacing-base);
}

.meta-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--gray-700);
    font-size: var(--font-sm);
}

.meta-item i {
    color: var(--primary);
    width: 16px;
}

.availability-status {
    margin-top: var(--spacing-base);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-sm);
    font-weight: 600;
}

.status-badge.available {
    background: var(--success-light);
    color: var(--success-dark);
}

.status-badge.unavailable {
    background: var(--danger-light);
    color: var(--danger-dark);
}

.provider-actions {
    margin-top: var(--spacing-lg);
}

.provider-actions .btn {
    width: 100%;
    justify-content: center;
}

.provider-management-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.provider-management-actions .btn {
    flex: 1;
}

.services-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.services-header h2 {
    margin: 0;
}

.service-management-actions {
    display: flex;
    gap: var(--spacing-xs);
    justify-content: flex-end;
}

.service-management-actions .btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-sm);
}

.empty-state-content {
    text-align: center;
    padding: var(--spacing-2xl);
}

.empty-state-content i {
    font-size: 3rem;
    color: var(--gray-400);
    margin-bottom: var(--spacing-base);
}

.empty-state-content h3 {
    color: var(--gray-600);
    margin-bottom: var(--spacing-sm);
}

.empty-state-content p {
    color: var(--gray-500);
    margin-bottom: var(--spacing-lg);
}

.provider-categories,
.provider-pet-categories,
.provider-bio,
.provider-services,
.provider-availability,
.provider-gallery,
.provider-reviews {
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-xl);
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
}

.provider-categories h2,
.provider-pet-categories h2,
.provider-bio h2,
.provider-services .services-header h2,
.provider-availability h2,
.provider-gallery h2,
.provider-reviews h2 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-xl);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.provider-categories h2 i,
.provider-pet-categories h2 i {
    color: var(--primary);
    font-size: var(--font-lg);
}

/* Service Categories Styles */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-base);
}

.category-badge {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-base);
    padding: var(--spacing-base);
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    transition: var(--transition-base);
    cursor: pointer;
}

.category-badge:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary);
}

.category-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-light);
    border-radius: var(--radius-lg);
    flex-shrink: 0;
}

.category-icon img {
    width: 32px;
    height: 32px;
    object-fit: cover;
    border-radius: var(--radius-sm);
}

.category-icon i {
    font-size: var(--font-xl);
    color: var(--primary);
}

.category-info {
    flex: 1;
}

.category-info h3 {
    color: var(--gray-800);
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-base);
    font-weight: var(--fw-semibold);
}

.category-info p {
    color: var(--gray-600);
    margin: 0;
    font-size: var(--font-sm);
    line-height: 1.4;
}

/* Pet Categories Styles */
.pet-categories-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.pet-category-tag {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-base);
    background: linear-gradient(135deg, var(--secondary-light) 0%, rgba(131, 43, 184, 0.1) 100%);
    color: var(--secondary-dark);
    border: 1px solid var(--secondary);
    border-radius: var(--radius-full);
    font-size: var(--font-sm);
    font-weight: var(--fw-medium);
    transition: var(--transition-base);
}

.pet-category-tag:hover {
    background: var(--secondary-light);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.pet-category-tag img {
    width: 20px;
    height: 20px;
    object-fit: cover;
    border-radius: var(--radius-sm);
}

.pet-category-tag i {
    font-size: var(--font-sm);
}

.provider-bio p {
    color: var(--gray-700);
    line-height: 1.6;
    margin: 0;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.service-card {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    transition: var(--transition-base);
}

.service-card:hover {
    box-shadow: var(--shadow-sm);
    transform: translateY(-2px);
}

.service-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}

.service-header h3 {
    color: var(--primary);
    margin: 0;
    font-size: var(--font-lg);
}

.service-price {
    color: var(--primary);
    font-weight: 600;
    font-size: var(--font-lg);
}

.service-description {
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
    line-height: 1.5;
}

.service-duration {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--gray-600);
    font-size: var(--font-sm);
    margin-bottom: var(--spacing-base);
}

.service-duration i {
    color: var(--primary);
}

.service-actions {
    text-align: right;
}

.availability-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-base);
}

.availability-day h4 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-base);
}

.time-slots {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.time-slot {
    background: var(--primary-light);
    color: var(--primary-dark);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-sm);
    text-align: center;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-base);
}

.gallery-item {
    position: relative;
    border-radius: var(--radius-md);
    overflow: hidden;
    aspect-ratio: 1;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.gallery-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.7);
    color: var(--white);
    padding: var(--spacing-sm);
    font-size: var(--font-sm);
}

.reviews-list {
    display: grid;
    gap: var(--spacing-base);
}

.review-card {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    padding: var(--spacing-base);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}

.reviewer-info strong {
    color: var(--gray-800);
    display: block;
    margin-bottom: var(--spacing-xs);
}

.review-rating {
    color: var(--warning);
    font-size: var(--font-sm);
}

.review-date {
    color: var(--gray-500);
    font-size: var(--font-sm);
}

.review-comment {
    color: var(--gray-700);
    line-height: 1.5;
    margin: 0;
}

.empty-state {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--gray-500);
}

.alert {
    padding: var(--spacing-base);
    border-radius: var(--radius-md);
    border: 1px solid;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-sm);
    margin-top: var(--spacing-base);
}

.alert-info {
    background-color: var(--info-light);
    border-color: var(--info);
    color: var(--info-dark);
}

.alert i {
    font-size: var(--font-base);
}

@media (max-width: 768px) {
    .provider-header {
        flex-direction: column;
        text-align: center;
    }

    .provider-meta {
        justify-content: center;
    }

    .provider-management-actions {
        flex-direction: column;
    }

    .services-header {
        flex-direction: column;
        gap: var(--spacing-base);
        align-items: stretch;
    }

    .service-management-actions {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .categories-grid {
        grid-template-columns: 1fr;
    }

    .pet-categories-list {
        justify-content: center;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .availability-grid {
        grid-template-columns: 1fr;
    }

    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>
{% endblock %}
