from django.contrib import admin
from .models import (
    ServiceCategory, ServiceProvider, Service, Availability,
    Booking, ServiceReview, ServiceProviderGallery, PredefinedService
)


class ServiceProviderGalleryInline(admin.TabularInline):
    model = ServiceProviderGallery
    extra = 1


class AvailabilityInline(admin.TabularInline):
    model = Availability
    extra = 0


class ServiceInline(admin.TabularInline):
    model = Service
    extra = 0


@admin.register(ServiceCategory)
class ServiceCategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'slug', 'icon_class', 'provider_count', 'has_icon')
    list_filter = ('icon_class',)
    search_fields = ('name', 'description')
    prepopulated_fields = {'slug': ('name',)}
    readonly_fields = ('provider_count',)

    def provider_count(self, obj):
        return obj.providers.count()
    provider_count.short_description = 'Providers'

    def has_icon(self, obj):
        return bool(obj.icon)
    has_icon.boolean = True
    has_icon.short_description = 'Has Image'


@admin.register(ServiceProvider)
class ServiceProviderAdmin(admin.ModelAdmin):
    list_display = ('user', 'experience_years', 'hourly_rate', 'rating', 'reviews_count', 'is_available')
    list_filter = ('is_available', 'categories', 'pet_categories')
    search_fields = ('user__username', 'bio')
    inlines = [ServiceProviderGalleryInline, AvailabilityInline, ServiceInline]


@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    list_display = ('name', 'provider', 'category', 'price', 'duration', 'is_available')
    list_filter = ('category', 'is_available')
    search_fields = ('name', 'description', 'provider__user__username')


@admin.register(Availability)
class AvailabilityAdmin(admin.ModelAdmin):
    list_display = ('provider', 'day_of_week', 'start_time', 'end_time')
    list_filter = ('day_of_week',)
    search_fields = ('provider__user__username',)


class ServiceReviewInline(admin.TabularInline):
    model = ServiceReview
    extra = 0
    readonly_fields = ('rating', 'comment', 'created_at')
    can_delete = False


@admin.register(Booking)
class BookingAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'service', 'date', 'start_time', 'end_time', 'status', 'total_price')
    list_filter = ('status', 'date')
    search_fields = ('user__username', 'service__name', 'notes')
    inlines = [ServiceReviewInline]


@admin.register(ServiceReview)
class ServiceReviewAdmin(admin.ModelAdmin):
    list_display = ('booking', 'rating', 'created_at')
    list_filter = ('rating', 'created_at')
    search_fields = ('booking__user__username', 'booking__service__name', 'comment')
    readonly_fields = ('created_at',)


@admin.register(PredefinedService)
class PredefinedServiceAdmin(admin.ModelAdmin):
    list_display = ('name', 'category', 'suggested_price', 'suggested_duration', 'is_active')
    list_filter = ('category', 'is_active')
    search_fields = ('name', 'description', 'category__name')
    list_editable = ('is_active',)
    ordering = ('category', 'name')
