{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}My Bookings - PetPaw{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            {% include 'components/provider_sidebar.html' %}
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Bookings Header -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white">
                    <ul class="nav nav-tabs card-header-tabs">
                        <li class="nav-item">
                            <a class="nav-link {% if booking_filter == 'all' %}active{% endif %}" href="{% url 'provider-bookings' %}?filter=all">All Bookings</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if booking_filter == 'pending' %}active{% endif %}" href="{% url 'provider-bookings' %}?filter=pending">Pending</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if booking_filter == 'confirmed' %}active{% endif %}" href="{% url 'provider-bookings' %}?filter=confirmed">Confirmed</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if booking_filter == 'completed' %}active{% endif %}" href="{% url 'provider-bookings' %}?filter=completed">Completed</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if booking_filter == 'cancelled' %}active{% endif %}" href="{% url 'provider-bookings' %}?filter=cancelled">Cancelled</a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    {% if bookings %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Client</th>
                                        <th>Pet</th>
                                        <th>Service</th>
                                        <th>Date & Time</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for booking in bookings %}
                                    <tr>
                                        <td>{{ booking.user.get_full_name|default:booking.user.username }}</td>
                                        <td>{{ booking.pet.name }}</td>
                                        <td>{{ booking.service.name }}</td>
                                        <td>{{ booking.date|date:"M d, Y" }} at {{ booking.start_time|time:"g:i A" }}</td>
                                        <td>
                                            {% if booking.status == 'pending' %}
                                                <span class="badge bg-warning text-dark">Pending</span>
                                            {% elif booking.status == 'confirmed' %}
                                                <span class="badge bg-success">Confirmed</span>
                                            {% elif booking.status == 'completed' %}
                                                <span class="badge bg-info">Completed</span>
                                            {% elif booking.status == 'cancelled' %}
                                                <span class="badge bg-danger">Cancelled</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="action-dropdown" data-booking-id="{{ booking.id }}">
                                                <button class="btn btn-sm btn-primary action-dropdown-toggle" type="button" onclick="toggleActionDropdown({{ booking.id }})">
                                                    <i class="fas fa-cog"></i>
                                                    Actions
                                                    <i class="fas fa-chevron-down"></i>
                                                </button>
                                                <div class="action-dropdown-menu" id="actionMenu{{ booking.id }}">
                                                    <a class="action-dropdown-item" href="{% url 'booking-detail' booking.id %}">
                                                        <i class="fas fa-eye"></i>
                                                        View Details
                                                    </a>
                                                    {% if booking.status == 'pending' %}
                                                        <form action="{% url 'update-booking-status' booking.id %}" method="post" class="action-form">
                                                            {% csrf_token %}
                                                            <input type="hidden" name="status" value="confirmed">
                                                            <button type="submit" class="action-dropdown-item action-confirm">
                                                                <i class="fas fa-check"></i>
                                                                Confirm Booking
                                                            </button>
                                                        </form>
                                                        <form action="{% url 'update-booking-status' booking.id %}" method="post" class="action-form">
                                                            {% csrf_token %}
                                                            <input type="hidden" name="status" value="cancelled">
                                                            <button type="submit" class="action-dropdown-item action-decline" onclick="return confirm('Are you sure you want to decline this booking?')">
                                                                <i class="fas fa-times"></i>
                                                                Decline Booking
                                                            </button>
                                                        </form>
                                                    {% elif booking.status == 'confirmed' %}
                                                        <form action="{% url 'update-booking-status' booking.id %}" method="post" class="action-form">
                                                            {% csrf_token %}
                                                            <input type="hidden" name="status" value="completed">
                                                            <button type="submit" class="action-dropdown-item action-complete">
                                                                <i class="fas fa-check-circle"></i>
                                                                Mark as Completed
                                                            </button>
                                                        </form>
                                                        <form action="{% url 'update-booking-status' booking.id %}" method="post" class="action-form">
                                                            {% csrf_token %}
                                                            <input type="hidden" name="status" value="cancelled">
                                                            <button type="submit" class="action-dropdown-item action-decline" onclick="return confirm('Are you sure you want to cancel this booking?')">
                                                                <i class="fas fa-ban"></i>
                                                                Cancel Booking
                                                            </button>
                                                        </form>
                                                    {% else %}
                                                        <div class="action-dropdown-item action-disabled">
                                                            <i class="fas fa-info-circle"></i>
                                                            No actions available
                                                        </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if is_paginated %}
                        <nav aria-label="Page navigation" class="mt-4">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1{% if booking_filter %}&filter={{ booking_filter }}{% endif %}" aria-label="First">
                                            <span aria-hidden="true">&laquo;&laquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if booking_filter %}&filter={{ booking_filter }}{% endif %}" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                {% endif %}

                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <li class="page-item"><a class="page-link" href="?page={{ num }}{% if booking_filter %}&filter={{ booking_filter }}{% endif %}">{{ num }}</a></li>
                                    {% endif %}
                                {% endfor %}

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if booking_filter %}&filter={{ booking_filter }}{% endif %}" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if booking_filter %}&filter={{ booking_filter }}{% endif %}" aria-label="Last">
                                            <span aria-hidden="true">&raquo;&raquo;</span>
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}

                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <h5>No Bookings Found</h5>
                            <p class="text-muted">
                                {% if booking_filter == 'all' %}
                                    You don't have any bookings yet.
                                {% else %}
                                    You don't have any {{ booking_filter }} bookings.
                                {% endif %}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Custom Action Dropdown Styles */
.action-dropdown {
    position: relative;
    display: inline-block;
}

.action-dropdown-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-sm);
    font-weight: var(--fw-medium);
    transition: var(--transition-base);
    border: 1px solid var(--primary);
    background: var(--primary);
    color: var(--white);
    cursor: pointer;
}

.action-dropdown-toggle:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.action-dropdown-toggle i:last-child {
    transition: transform 0.2s ease;
}

.action-dropdown-toggle.active i:last-child {
    transform: rotate(180deg);
}

.action-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    z-index: 1000;
    display: none;
    overflow: hidden;
}

.action-dropdown-menu.show {
    display: block;
    animation: dropdownFadeIn 0.2s ease;
}

@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.action-dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-base);
    color: var(--gray-700);
    text-decoration: none;
    font-size: var(--font-sm);
    transition: var(--transition-base);
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

.action-dropdown-item:hover {
    background: var(--gray-50);
    color: var(--gray-900);
    text-decoration: none;
}

.action-dropdown-item i {
    width: 16px;
    text-align: center;
}

.action-confirm:hover {
    background: var(--success-light);
    color: var(--success-dark);
}

.action-complete:hover {
    background: var(--info-light);
    color: var(--info-dark);
}

.action-decline:hover {
    background: var(--danger-light);
    color: var(--danger-dark);
}

.action-disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.action-disabled:hover {
    background: none;
    color: var(--gray-700);
}

.action-form {
    margin: 0;
    width: 100%;
}

/* Status badges improvements */
.badge {
    font-size: var(--font-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-weight: var(--fw-semibold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bg-warning {
    background: var(--warning) !important;
    color: var(--dark) !important;
}

.bg-success {
    background: var(--success) !important;
    color: var(--white) !important;
}

.bg-info {
    background: var(--info) !important;
    color: var(--white) !important;
}

.bg-danger {
    background: var(--danger) !important;
    color: var(--white) !important;
}

/* Table improvements */
.table {
    border-collapse: separate;
    border-spacing: 0;
}

.table th {
    background: var(--gray-50);
    border-bottom: 2px solid var(--gray-200);
    font-weight: var(--fw-semibold);
    color: var(--gray-700);
    padding: var(--spacing-base);
}

.table td {
    padding: var(--spacing-base);
    vertical-align: middle;
    border-bottom: 1px solid var(--gray-100);
}

.table-hover tbody tr:hover {
    background: var(--gray-50);
}

/* Responsive improvements */
@media (max-width: 768px) {
    .action-dropdown-menu {
        right: auto;
        left: 0;
        min-width: 180px;
    }

    .table-responsive {
        font-size: var(--font-sm);
    }

    .action-dropdown-toggle {
        padding: var(--spacing-xs);
        font-size: var(--font-xs);
    }
}
</style>

<script>
// Global variable to track currently open dropdown
let currentOpenDropdown = null;

function toggleActionDropdown(bookingId) {
    const menu = document.getElementById('actionMenu' + bookingId);
    const toggle = menu.previousElementSibling;

    // Close any currently open dropdown
    if (currentOpenDropdown && currentOpenDropdown !== menu) {
        currentOpenDropdown.classList.remove('show');
        currentOpenDropdown.previousElementSibling.classList.remove('active');
    }

    // Toggle current dropdown
    if (menu.classList.contains('show')) {
        menu.classList.remove('show');
        toggle.classList.remove('active');
        currentOpenDropdown = null;
    } else {
        menu.classList.add('show');
        toggle.classList.add('active');
        currentOpenDropdown = menu;
    }
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('.action-dropdown')) {
        if (currentOpenDropdown) {
            currentOpenDropdown.classList.remove('show');
            currentOpenDropdown.previousElementSibling.classList.remove('active');
            currentOpenDropdown = null;
        }
    }
});

// Close dropdown when pressing Escape
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape' && currentOpenDropdown) {
        currentOpenDropdown.classList.remove('show');
        currentOpenDropdown.previousElementSibling.classList.remove('active');
        currentOpenDropdown = null;
    }
});

// Add loading states to form submissions
document.addEventListener('DOMContentLoaded', function() {
    const actionForms = document.querySelectorAll('.action-form');

    actionForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const button = form.querySelector('button[type="submit"]');
            if (button) {
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
            }
        });
    });
});
</script>
{% endblock %}
