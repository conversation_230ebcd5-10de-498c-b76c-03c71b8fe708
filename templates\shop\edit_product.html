{% extends 'base.html' %}
{% load static %}

{% block title %}Edit Product - Seller Dashboard{% endblock %}

{% block content %}
<div class="container">
    <div class="seller-dashboard">
        <div class="dashboard-sidebar">
            {% include 'components/seller_sidebar.html' %}
        </div>

        <div class="dashboard-main">
            <div class="product-form-container">
                <div class="product-form-header">
                    <h1>Edit Product</h1>
                    <p>Update the details for "{{ product.name }}"</p>
                </div>

                <div class="product-form">
                    <form method="POST" enctype="multipart/form-data">
                        {% csrf_token %}

                        <div class="form-section">
                            <h3><i class="fas fa-info-circle"></i> Basic Information</h3>
                            
                            <div class="form-group">
                                <label for="{{ form.name.id_for_label }}">Product Name *</label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="error-message">{{ form.name.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="{{ form.category.id_for_label }}">Product Category *</label>
                                    {{ form.category }}
                                    {% if form.category.errors %}
                                        <div class="error-message">{{ form.category.errors.0 }}</div>
                                    {% endif %}
                                </div>

                                <div class="form-group">
                                    <label for="{{ form.pet_category.id_for_label }}">Pet Category</label>
                                    {{ form.pet_category }}
                                    {% if form.pet_category.errors %}
                                        <div class="error-message">{{ form.pet_category.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="{{ form.description.id_for_label }}">Description *</label>
                                {{ form.description }}
                                {% if form.description.errors %}
                                    <div class="error-message">{{ form.description.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="form-section">
                            <h3><i class="fas fa-dollar-sign"></i> Pricing & Inventory</h3>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="{{ form.price.id_for_label }}">Price *</label>
                                    {{ form.price }}
                                    {% if form.price.errors %}
                                        <div class="error-message">{{ form.price.errors.0 }}</div>
                                    {% endif %}
                                </div>

                                <div class="form-group">
                                    <label for="{{ form.discount_price.id_for_label }}">Discount Price</label>
                                    {{ form.discount_price }}
                                    {% if form.discount_price.errors %}
                                        <div class="error-message">{{ form.discount_price.errors.0 }}</div>
                                    {% endif %}
                                </div>

                                <div class="form-group">
                                    <label for="{{ form.stock.id_for_label }}">Stock Quantity *</label>
                                    {{ form.stock }}
                                    {% if form.stock.errors %}
                                        <div class="error-message">{{ form.stock.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3><i class="fas fa-image"></i> Product Image</h3>
                            
                            {% if product.image %}
                                <div class="current-image">
                                    <label>Current Image:</label>
                                    <img src="{{ product.image.url }}" alt="{{ product.name }}" class="product-preview">
                                </div>
                            {% endif %}
                            
                            <div class="form-group">
                                <label for="{{ form.image.id_for_label }}">Update Product Image</label>
                                {{ form.image }}
                                {% if form.image.errors %}
                                    <div class="error-message">{{ form.image.errors.0 }}</div>
                                {% endif %}
                                <small class="form-help">Leave empty to keep current image</small>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3><i class="fas fa-toggle-on"></i> Availability</h3>
                            
                            <div class="form-group checkbox-group">
                                {{ form.is_available }}
                                <label for="{{ form.is_available.id_for_label }}">Product is available for purchase</label>
                            </div>
                        </div>

                        <div class="form-actions">
                            <a href="{% url 'seller-products' %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i>
                                Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                Update Product
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.seller-dashboard {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: var(--spacing-xl);
    padding: var(--spacing-xl) 0;
}

.product-form-container {
    max-width: 800px;
}

.product-form-header {
    margin-bottom: var(--spacing-2xl);
}

.product-form-header h1 {
    color: var(--primary);
    margin-bottom: var(--spacing-xs);
}

.product-form-header p {
    color: var(--text-muted);
    font-size: var(--font-lg);
}

.product-form {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-sm);
}

.form-section {
    margin-bottom: var(--spacing-2xl);
}

.form-section h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-light);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: var(--fw-medium);
    color: var(--text);
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-base);
    transition: var(--transition-base);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.current-image {
    margin-bottom: var(--spacing-base);
}

.current-image label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: var(--fw-medium);
    color: var(--text);
}

.product-preview {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border-radius: var(--border-radius);
    border: 2px solid var(--border-color);
}

.form-help {
    display: block;
    margin-top: var(--spacing-xs);
    color: var(--text-muted);
    font-size: var(--font-sm);
}

.error-message {
    color: var(--danger);
    font-size: var(--font-sm);
    margin-top: var(--spacing-xs);
}

.form-actions {
    display: flex;
    gap: var(--spacing-base);
    justify-content: flex-end;
    margin-top: var(--spacing-2xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--font-base);
    font-weight: var(--fw-medium);
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-base);
}

.btn-primary {
    background-color: var(--primary);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: var(--gray-200);
    color: var(--text);
}

.btn-secondary:hover {
    background-color: var(--gray-300);
}

@media (max-width: 992px) {
    .seller-dashboard {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .product-form {
        padding: var(--spacing-lg);
    }
}
</style>
{% endblock %}
