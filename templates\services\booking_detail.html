{% extends 'base.html' %}
{% load static %}

{% block title %}Booking Details - PetPaw{% endblock %}

{% block content %}
<div class="container">
    <div class="booking-detail-header">
        <a href="{% url 'booking-list' %}" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Back to Bookings
        </a>
        <h1>Booking Details</h1>
    </div>

    <div class="booking-detail-card">
        <div class="booking-status-header">
            <div class="service-info">
                <div class="service-title-section">
                    <h2>{{ booking.service.name }}</h2>
                    <div class="service-meta">
                        <span class="provider-name">
                            <i class="fas fa-user-tie"></i>
                            {{ booking.service.provider.user.full_name|default:booking.service.provider.user.email }}
                        </span>
                        <span class="booking-id">
                            <i class="fas fa-hashtag"></i>
                            Booking #{{ booking.pk }}
                        </span>
                    </div>
                </div>
            </div>
            <div class="status-section">
                <div class="status-badge-container">
                    <span class="status-badge status-{{ booking.status }}">
                        {% if booking.status == 'pending' %}
                            <i class="fas fa-clock"></i>
                        {% elif booking.status == 'confirmed' %}
                            <i class="fas fa-check-circle"></i>
                        {% elif booking.status == 'completed' %}
                            <i class="fas fa-check-double"></i>
                        {% elif booking.status == 'cancelled' %}
                            <i class="fas fa-times-circle"></i>
                        {% endif %}
                        {{ booking.get_status_display }}
                    </span>
                    <span class="booking-date">
                        <i class="fas fa-calendar-alt"></i>
                        {{ booking.created_at|date:"M d, Y" }}
                    </span>
                </div>
            </div>
        </div>

        <div class="booking-details-grid">
            <div class="detail-section">
                <h3>Booking Information</h3>
                <div class="detail-list">
                    <div class="detail-item">
                        <span class="label">Date:</span>
                        <span class="value">{{ booking.date|date:"F d, Y" }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">Time:</span>
                        <span class="value">{{ booking.start_time|time:"g:i A" }} - {{ booking.end_time|time:"g:i A" }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">Duration:</span>
                        <span class="value">{{ booking.duration }} hours</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">Total Price:</span>
                        <span class="value price">${{ booking.total_price }}</span>
                    </div>
                    {% if booking.pet %}
                        <div class="detail-item">
                            <span class="label">Pet:</span>
                            <span class="value">{{ booking.pet.name }}</span>
                        </div>
                    {% endif %}
                </div>
            </div>

            <div class="detail-section">
                <h3>Service Details</h3>
                <div class="service-card">
                    <div class="service-description">
                        <p>{{ booking.service.description }}</p>
                    </div>
                    <div class="service-price">
                        <span class="price-label">Hourly Rate:</span>
                        <span class="price-value">₹{{ booking.service.price }}</span>
                    </div>
                </div>
            </div>
        </div>

        {% if booking.notes %}
            <div class="notes-section">
                <h3>Special Notes</h3>
                <div class="notes-content">
                    <p>{{ booking.notes }}</p>
                </div>
            </div>
        {% endif %}

        <div class="actions-section">
            <div class="actions-header">
                <h3>
                    <i class="fas fa-cogs"></i>
                    Available Actions
                </h3>
                <p class="actions-description">
                    {% if booking.user == request.user %}
                        Manage your booking below
                    {% elif booking.service.provider.user == request.user %}
                        Manage this customer's booking
                    {% endif %}
                </p>
            </div>

            <div class="actions-buttons">
                {% if booking.user == request.user %}
                    <!-- Customer actions -->
                    {% if booking.status == 'pending' %}
                        <button class="btn btn-danger action-btn" onclick="cancelBooking({{ booking.pk }})" id="cancelBtn">
                            <span class="btn-content">
                                <i class="fas fa-times"></i>
                                Cancel Booking
                            </span>
                            <span class="btn-loading" style="display: none;">
                                <i class="fas fa-spinner fa-spin"></i>
                                Cancelling...
                            </span>
                        </button>
                    {% endif %}

                    {% if can_review %}
                        <button class="btn btn-primary action-btn" onclick="showReviewForm()">
                            <i class="fas fa-star"></i>
                            Write Review
                        </button>
                    {% endif %}

                    {% if booking.status == 'cancelled' or booking.status == 'completed' %}
                        <div class="action-info">
                            <i class="fas fa-info-circle"></i>
                            {% if booking.status == 'cancelled' %}
                                This booking has been cancelled. No further actions are available.
                            {% else %}
                                This booking has been completed.
                                {% if not can_review and not booking.review %}
                                    Thank you for using our service!
                                {% endif %}
                            {% endif %}
                        </div>
                    {% endif %}

                {% elif booking.service.provider.user == request.user %}
                    <!-- Provider actions -->
                    {% if booking.status == 'pending' %}
                        <form method="post" action="{% url 'update-booking-status' booking.pk %}" class="action-form">
                            {% csrf_token %}
                            <input type="hidden" name="status" value="confirmed">
                            <button type="submit" class="btn btn-success action-btn">
                                <i class="fas fa-check"></i>
                                Confirm Booking
                            </button>
                        </form>
                        <form method="post" action="{% url 'update-booking-status' booking.pk %}" class="action-form">
                            {% csrf_token %}
                            <input type="hidden" name="status" value="cancelled">
                            <button type="submit" class="btn btn-danger action-btn">
                                <i class="fas fa-times"></i>
                                Decline Booking
                            </button>
                        </form>
                    {% elif booking.status == 'confirmed' %}
                        <form method="post" action="{% url 'update-booking-status' booking.pk %}" class="action-form">
                            {% csrf_token %}
                            <input type="hidden" name="status" value="completed">
                            <button type="submit" class="btn btn-primary action-btn">
                                <i class="fas fa-check-circle"></i>
                                Mark as Completed
                            </button>
                        </form>
                    {% elif booking.status == 'cancelled' or booking.status == 'completed' %}
                        <div class="action-info">
                            <i class="fas fa-info-circle"></i>
                            {% if booking.status == 'cancelled' %}
                                This booking has been cancelled. No further actions are available.
                            {% else %}
                                This booking has been marked as completed.
                            {% endif %}
                        </div>
                    {% endif %}
                {% endif %}
            </div>
        </div>
    </div>

    {% if can_review %}
        <!-- Review Form Modal -->
        <div id="reviewModal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Write a Review</h3>
                    <button class="modal-close" onclick="hideReviewForm()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form method="post" action="{% url 'add-service-review' booking.pk %}">
                    {% csrf_token %}
                    <div class="modal-body">
                        {{ review_form.as_p }}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="hideReviewForm()">Cancel</button>
                        <button type="submit" class="btn btn-primary">Submit Review</button>
                    </div>
                </form>
            </div>
        </div>
    {% endif %}
</div>

<style>
.booking-detail-header {
    margin-bottom: var(--spacing-xl);
}

.back-link {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--primary);
    text-decoration: none;
    margin-bottom: var(--spacing-lg);
    font-weight: 500;
    transition: var(--transition-base);
}

.back-link:hover {
    color: var(--primary-dark);
}

.booking-detail-header h1 {
    color: var(--gray-800);
    margin: 0;
}

.booking-detail-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: var(--transition-base);
}

.booking-detail-card:hover {
    box-shadow: var(--shadow-lg);
}

/* Notification styles */
.notification {
    font-family: var(--font-family-sans);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.notification-content i {
    font-size: var(--font-base);
    flex-shrink: 0;
}

.booking-status-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
    border-bottom: 1px solid var(--gray-200);
    position: relative;
}

.booking-status-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary), var(--secondary));
}

.service-title-section h2 {
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-2xl);
    font-weight: var(--fw-bold);
}

.service-meta {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.provider-name,
.booking-id {
    color: var(--gray-600);
    font-size: var(--font-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.provider-name i,
.booking-id i {
    color: var(--primary);
    width: 16px;
}

.status-badge-container {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--spacing-sm);
}

.status-badge {
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-full);
    font-weight: 600;
    text-transform: uppercase;
    font-size: var(--font-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    box-shadow: var(--shadow-sm);
    border: 2px solid transparent;
}

.booking-date {
    color: var(--gray-500);
    font-size: var(--font-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* Status badge colors */
.status-pending {
    background: linear-gradient(135deg, var(--warning), var(--warning-dark));
    color: var(--dark);
    border-color: var(--warning-dark);
}

.status-confirmed {
    background: linear-gradient(135deg, var(--info), var(--info-dark));
    color: var(--white);
    border-color: var(--info-dark);
}

.status-completed {
    background: linear-gradient(135deg, var(--success), var(--success-dark));
    color: var(--white);
    border-color: var(--success-dark);
}

.status-cancelled {
    background: linear-gradient(135deg, var(--danger), var(--danger-dark));
    color: var(--white);
    border-color: var(--danger-dark);
}

.booking-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    padding: var(--spacing-xl);
}

.detail-section h3 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-base);
    font-size: var(--font-lg);
}

.detail-list {
    display: grid;
    gap: var(--spacing-sm);
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--gray-100);
}

.detail-item:last-child {
    border-bottom: none;
}

.label {
    color: var(--gray-600);
    font-weight: 500;
}

.value {
    color: var(--gray-800);
    font-weight: 600;
}

.value.price {
    color: var(--primary);
    font-size: var(--font-lg);
}

.service-card {
    background: var(--gray-50);
    padding: var(--spacing-base);
    border-radius: var(--radius-md);
    border: 1px solid var(--gray-200);
}

.service-description {
    margin-bottom: var(--spacing-base);
}

.service-price {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.price-label {
    color: var(--gray-600);
}

.price-value {
    color: var(--primary);
    font-weight: 600;
}

.notes-section {
    padding: var(--spacing-xl);
    border-top: 1px solid var(--gray-200);
}

.notes-section h3 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-base);
}

.notes-content {
    background: var(--gray-50);
    padding: var(--spacing-base);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--primary);
}

.actions-section {
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
    border-top: 1px solid var(--gray-200);
}

.actions-header {
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.actions-header h3 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    font-size: var(--font-lg);
}

.actions-header h3 i {
    color: var(--primary);
}

.actions-description {
    color: var(--gray-600);
    font-size: var(--font-sm);
    margin: 0;
}

.actions-buttons {
    display: flex;
    gap: var(--spacing-base);
    flex-wrap: wrap;
    justify-content: center;
}

.action-form {
    display: inline-block;
}

.action-btn {
    position: relative;
    min-width: 160px;
    padding: var(--spacing-base) var(--spacing-xl);
    font-weight: var(--fw-semibold);
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
    border: 2px solid transparent;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.action-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-content,
.btn-loading {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    justify-content: center;
}

.action-info {
    background: var(--info-light);
    color: var(--info-dark);
    padding: var(--spacing-base) var(--spacing-lg);
    border-radius: var(--radius-lg);
    border: 1px solid var(--info);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-sm);
    max-width: 400px;
    margin: 0 auto;
    text-align: center;
}

.action-info i {
    color: var(--info);
    flex-shrink: 0;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: var(--z-50);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--white);
    border-radius: var(--radius-lg);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-lg);
    color: var(--gray-500);
    cursor: pointer;
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

@media (max-width: 768px) {
    .booking-status-header {
        flex-direction: column;
        gap: var(--spacing-base);
        text-align: center;
    }

    .status-badge-container {
        align-items: center;
    }

    .service-meta {
        align-items: center;
    }

    .booking-details-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .actions-buttons {
        flex-direction: column;
        align-items: center;
    }

    .action-btn {
        width: 100%;
        max-width: 300px;
    }

    .action-info {
        text-align: left;
    }
}

@media (max-width: 480px) {
    .booking-detail-header h1 {
        font-size: var(--font-xl);
    }

    .service-title-section h2 {
        font-size: var(--font-xl);
    }

    .booking-detail-card {
        margin: 0 calc(-1 * var(--spacing-base));
        border-radius: 0;
    }

    .container {
        padding: 0 var(--spacing-sm);
    }
}
</style>

<script>
function showReviewForm() {
    document.getElementById('reviewModal').style.display = 'flex';
}

function hideReviewForm() {
    document.getElementById('reviewModal').style.display = 'none';
}

function cancelBooking(bookingId) {
    // Show custom confirmation dialog
    if (!confirm('Are you sure you want to cancel this booking? This action cannot be undone.')) {
        return;
    }

    const cancelBtn = document.getElementById('cancelBtn');
    const btnContent = cancelBtn.querySelector('.btn-content');
    const btnLoading = cancelBtn.querySelector('.btn-loading');

    // Show loading state
    cancelBtn.disabled = true;
    btnContent.style.display = 'none';
    btnLoading.style.display = 'flex';

    fetch(`/services/bookings/${bookingId}/cancel/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCookie('csrftoken'),
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            showNotification('Booking cancelled successfully!', 'success');

            // Reload page after a short delay
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            // Reset button state
            cancelBtn.disabled = false;
            btnContent.style.display = 'flex';
            btnLoading.style.display = 'none';

            showNotification(data.error || 'Failed to cancel booking. Please try again.', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);

        // Reset button state
        cancelBtn.disabled = false;
        btnContent.style.display = 'flex';
        btnLoading.style.display = 'none';

        showNotification('Failed to cancel booking. Please try again.', 'error');
    });
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    // Add notification styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? 'var(--success)' : type === 'error' ? 'var(--danger)' : 'var(--info)'};
        color: var(--white);
        padding: var(--spacing-base) var(--spacing-lg);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        z-index: 1000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 300px;
        font-size: var(--font-sm);
    `;

    // Add to page
    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after delay
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}
