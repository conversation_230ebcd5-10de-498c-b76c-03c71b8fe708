{% extends 'base.html' %}
{% load static %}

{% block title %}Settings - Seller Dashboard{% endblock %}

{% block content %}
<div class="container">
    <div class="seller-dashboard">
        <div class="dashboard-sidebar">
            {% include 'components/seller_sidebar.html' %}
        </div>

        <div class="dashboard-main">
            <div class="settings-container">
                <div class="settings-header">
                    <h1>Store Settings</h1>
                    <p>Update your business information and store settings</p>
                </div>

                <div class="settings-form">
                    <form method="POST" enctype="multipart/form-data">
                        {% csrf_token %}

                        <div class="form-section">
                            <h3><i class="fas fa-store"></i> Business Information</h3>
                            
                            <div class="form-group">
                                <label for="{{ form.business_name.id_for_label }}">Business Name *</label>
                                {{ form.business_name }}
                                {% if form.business_name.errors %}
                                    <div class="error-message">{{ form.business_name.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <div class="form-group">
                                <label for="{{ form.business_description.id_for_label }}">Business Description *</label>
                                {{ form.business_description }}
                                {% if form.business_description.errors %}
                                    <div class="error-message">{{ form.business_description.errors.0 }}</div>
                                {% endif %}
                            </div>

                            <div class="form-group">
                                <label for="{{ form.business_address.id_for_label }}">Business Address *</label>
                                {{ form.business_address }}
                                {% if form.business_address.errors %}
                                    <div class="error-message">{{ form.business_address.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="form-section">
                            <h3><i class="fas fa-phone"></i> Contact Information</h3>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="{{ form.business_phone.id_for_label }}">Business Phone *</label>
                                    {{ form.business_phone }}
                                    {% if form.business_phone.errors %}
                                        <div class="error-message">{{ form.business_phone.errors.0 }}</div>
                                    {% endif %}
                                </div>

                                <div class="form-group">
                                    <label for="{{ form.business_email.id_for_label }}">Business Email *</label>
                                    {{ form.business_email }}
                                    {% if form.business_email.errors %}
                                        <div class="error-message">{{ form.business_email.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="{{ form.business_website.id_for_label }}">Business Website</label>
                                {{ form.business_website }}
                                {% if form.business_website.errors %}
                                    <div class="error-message">{{ form.business_website.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="form-section">
                            <h3><i class="fas fa-image"></i> Business Logo</h3>
                            
                            {% if seller.business_logo %}
                                <div class="current-logo">
                                    <label>Current Logo:</label>
                                    <img src="{{ seller.business_logo.url }}" alt="{{ seller.business_name }}" class="logo-preview">
                                </div>
                            {% endif %}
                            
                            <div class="form-group">
                                <label for="{{ form.business_logo.id_for_label }}">Update Business Logo</label>
                                {{ form.business_logo }}
                                {% if form.business_logo.errors %}
                                    <div class="error-message">{{ form.business_logo.errors.0 }}</div>
                                {% endif %}
                                <small class="form-help">Leave empty to keep current logo</small>
                            </div>
                        </div>

                        <div class="form-actions">
                            <a href="{% url 'seller-dashboard' %}" class="btn btn-secondary">
                                <i class="fas fa-times"></i>
                                Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                Save Settings
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Store Statistics -->
                <div class="store-stats">
                    <h3>Store Statistics</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-label">Store Rating</div>
                            <div class="stat-value">{{ seller.rating|floatformat:1 }}/5.0</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Total Reviews</div>
                            <div class="stat-value">{{ seller.reviews_count }}</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Total Sales</div>
                            <div class="stat-value">{{ seller.total_sales }}</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Member Since</div>
                            <div class="stat-value">{{ seller.created_at|date:"M Y" }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.seller-dashboard {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: var(--spacing-xl);
    padding: var(--spacing-xl) 0;
}

.settings-container {
    max-width: 800px;
}

.settings-header {
    margin-bottom: var(--spacing-2xl);
}

.settings-header h1 {
    color: var(--primary);
    margin-bottom: var(--spacing-xs);
}

.settings-header p {
    color: var(--text-muted);
    font-size: var(--font-lg);
}

.settings-form {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-2xl);
}

.form-section {
    margin-bottom: var(--spacing-2xl);
}

.form-section h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-light);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: var(--fw-medium);
    color: var(--text);
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-base);
    transition: var(--transition-base);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.current-logo {
    margin-bottom: var(--spacing-base);
}

.current-logo label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: var(--fw-medium);
    color: var(--text);
}

.logo-preview {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: var(--border-radius);
    border: 2px solid var(--border-color);
}

.form-help {
    display: block;
    margin-top: var(--spacing-xs);
    color: var(--text-muted);
    font-size: var(--font-sm);
}

.error-message {
    color: var(--danger);
    font-size: var(--font-sm);
    margin-top: var(--spacing-xs);
}

.form-actions {
    display: flex;
    gap: var(--spacing-base);
    justify-content: flex-end;
    margin-top: var(--spacing-2xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--font-base);
    font-weight: var(--fw-medium);
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-base);
}

.btn-primary {
    background-color: var(--primary);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: var(--gray-200);
    color: var(--text);
}

.btn-secondary:hover {
    background-color: var(--gray-300);
}

.store-stats {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
}

.store-stats h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-lg);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-lg);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-base);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--white);
}

.stat-label {
    color: var(--text-muted);
    font-size: var(--font-sm);
    margin-bottom: var(--spacing-xs);
}

.stat-value {
    color: var(--text);
    font-size: var(--font-lg);
    font-weight: var(--fw-bold);
}

@media (max-width: 992px) {
    .seller-dashboard {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .settings-form {
        padding: var(--spacing-lg);
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>
{% endblock %}
