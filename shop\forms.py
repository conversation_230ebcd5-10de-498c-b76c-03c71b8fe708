from django import forms
from .models import Review, Seller, Product, ProductCategory
from pets.models import PetCategory


class ReviewForm(forms.ModelForm):
    """Form for product reviews"""
    class Meta:
        model = Review
        fields = ('rating', 'comment')
        widgets = {
            'comment': forms.Textarea(attrs={'rows': 4, 'placeholder': 'Write your review here...'}),
        }


class CheckoutForm(forms.Form):
    """Form for checkout process"""
    shipping_address = forms.IntegerField(widget=forms.HiddenInput())
    billing_address = forms.IntegerField(widget=forms.HiddenInput())
    payment_method = forms.ChoiceField(
        choices=[
            ('credit_card', 'Credit Card'),
            ('paypal', 'PayPal'),
        ],
        widget=forms.RadioSelect()
    )


class SellerForm(forms.ModelForm):
    """Form for seller registration"""
    class Meta:
        model = Seller
        fields = (
            'business_name', 'business_description', 'business_address',
            'business_phone', 'business_email', 'business_website', 'business_logo'
        )
        widgets = {
            'business_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter your business name'
            }),
            'business_description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Describe your business and what you sell'
            }),
            'business_address': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Enter your business address'
            }),
            'business_phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Business phone number'
            }),
            'business_email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'Business email address'
            }),
            'business_website': forms.URLInput(attrs={
                'class': 'form-control',
                'placeholder': 'https://your-website.com (optional)'
            }),
            'business_logo': forms.FileInput(attrs={
                'class': 'form-control'
            }),
        }


class ProductForm(forms.ModelForm):
    """Form for adding/editing products"""
    class Meta:
        model = Product
        fields = (
            'name', 'category', 'pet_category', 'description',
            'price', 'discount_price', 'stock', 'image', 'is_available'
        )
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Product name'
            }),
            'category': forms.Select(attrs={
                'class': 'form-control'
            }),
            'pet_category': forms.Select(attrs={
                'class': 'form-control'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Describe your product'
            }),
            'price': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'placeholder': '0.00'
            }),
            'discount_price': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'placeholder': '0.00 (optional)'
            }),
            'stock': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Available quantity'
            }),
            'image': forms.FileInput(attrs={
                'class': 'form-control'
            }),
            'is_available': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
