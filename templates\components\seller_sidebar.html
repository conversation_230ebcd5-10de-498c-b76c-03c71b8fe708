<!-- Seller Sidebar Component -->
<div class="seller-sidebar">
    <div class="seller-profile-card">
        {% if seller.business_logo %}
            <img src="{{ seller.business_logo.url }}" 
                 alt="{{ seller.business_name }}" 
                 class="seller-logo">
        {% else %}
            <div class="seller-logo-placeholder">
                <i class="fas fa-store"></i>
            </div>
        {% endif %}
        <h5 class="seller-name">{{ seller.business_name }}</h5>
        <p class="seller-owner">{{ user.full_name|default:user.email }}</p>
        <div class="seller-rating">
            <div class="stars">
                {% for i in "12345" %}
                    {% if forloop.counter <= seller.rating %}
                        <i class="fas fa-star"></i>
                    {% else %}
                        <i class="far fa-star"></i>
                    {% endif %}
                {% endfor %}
            </div>
            <span class="rating-text">{{ seller.rating|floatformat:1 }} ({{ seller.reviews_count }} reviews)</span>
        </div>
        <div class="seller-status">
            {% if seller.is_active %}
                <span class="status-badge status-active">
                    <i class="fas fa-check-circle"></i> Active
                </span>
            {% else %}
                <span class="status-badge status-inactive">
                    <i class="fas fa-pause-circle"></i> Inactive
                </span>
            {% endif %}
        </div>
    </div>
    
    <nav class="seller-nav">
        <ul class="seller-nav-list">
            <li class="seller-nav-item">
                <a href="{% url 'seller-dashboard' %}" 
                   class="seller-nav-link {% if active_tab == 'dashboard' %}active{% endif %}">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </a>
            </li>
            <li class="seller-nav-item">
                <a href="{% url 'seller-products' %}" 
                   class="seller-nav-link {% if active_tab == 'products' %}active{% endif %}">
                    <i class="fas fa-boxes"></i>
                    My Products
                </a>
            </li>
            <li class="seller-nav-item">
                <a href="{% url 'add-product' %}" 
                   class="seller-nav-link {% if active_tab == 'add-product' %}active{% endif %}">
                    <i class="fas fa-plus-circle"></i>
                    Add Product
                </a>
            </li>
            <li class="seller-nav-item">
                <a href="{% url 'seller-orders' %}" 
                   class="seller-nav-link {% if active_tab == 'orders' %}active{% endif %}">
                    <i class="fas fa-shopping-cart"></i>
                    Orders
                </a>
            </li>
            <li class="seller-nav-item">
                <a href="{% url 'seller-settings' %}" 
                   class="seller-nav-link {% if active_tab == 'settings' %}active{% endif %}">
                    <i class="fas fa-cog"></i>
                    Settings
                </a>
            </li>
        </ul>
    </nav>
</div>

<style>
.seller-sidebar {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    height: fit-content;
    position: sticky;
    top: var(--spacing-lg);
}

.seller-profile-card {
    padding: var(--spacing-xl);
    text-align: center;
    border-bottom: 1px solid var(--border-color);
}

.seller-logo {
    width: 80px;
    height: 80px;
    border-radius: var(--border-radius);
    object-fit: cover;
    margin-bottom: var(--spacing-base);
    border: 2px solid var(--border-color);
}

.seller-logo-placeholder {
    width: 80px;
    height: 80px;
    background: var(--gray-200);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-base);
    color: var(--text-muted);
    font-size: var(--font-xl);
}

.seller-name {
    font-size: var(--font-lg);
    font-weight: var(--fw-semibold);
    color: var(--text);
    margin: 0 0 var(--spacing-xs) 0;
}

.seller-owner {
    color: var(--text-muted);
    font-size: var(--font-sm);
    margin: 0 0 var(--spacing-base) 0;
}

.seller-rating {
    margin-bottom: var(--spacing-base);
}

.stars {
    display: flex;
    justify-content: center;
    gap: 2px;
    margin-bottom: var(--spacing-xs);
}

.stars i {
    color: var(--warning);
    font-size: var(--font-sm);
}

.rating-text {
    font-size: var(--font-xs);
    color: var(--text-muted);
}

.seller-status {
    margin-top: var(--spacing-base);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: var(--font-xs);
    font-weight: var(--fw-medium);
}

.status-active {
    background: var(--success-light);
    color: var(--success-dark);
}

.status-inactive {
    background: var(--warning-light);
    color: var(--warning-dark);
}

.seller-nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.seller-nav-item {
    border-bottom: 1px solid var(--border-color);
}

.seller-nav-item:last-child {
    border-bottom: none;
}

.seller-nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-base) var(--spacing-xl);
    color: var(--text);
    text-decoration: none;
    transition: var(--transition-base);
    font-weight: var(--fw-medium);
}

.seller-nav-link:hover {
    background: var(--gray-50);
    color: var(--primary);
}

.seller-nav-link.active {
    background: var(--primary-light);
    color: var(--primary);
    border-right: 3px solid var(--primary);
}

.seller-nav-link i {
    width: 18px;
    text-align: center;
    font-size: var(--font-sm);
}

@media (max-width: 992px) {
    .seller-sidebar {
        position: static;
        margin-bottom: var(--spacing-lg);
    }
    
    .seller-profile-card {
        padding: var(--spacing-lg);
    }
    
    .seller-nav-list {
        display: flex;
        overflow-x: auto;
    }
    
    .seller-nav-item {
        border-bottom: none;
        border-right: 1px solid var(--border-color);
        flex-shrink: 0;
    }
    
    .seller-nav-item:last-child {
        border-right: none;
    }
    
    .seller-nav-link {
        padding: var(--spacing-sm) var(--spacing-base);
        white-space: nowrap;
    }
    
    .seller-nav-link.active {
        border-right: none;
        border-bottom: 3px solid var(--primary);
    }
}
</style>
