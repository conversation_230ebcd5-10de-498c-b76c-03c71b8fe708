from django.core.management.base import BaseCommand
from services.models import ServiceCategory, PredefinedService


class Command(BaseCommand):
    help = 'Create predefined services for all service categories'

    def handle(self, *args, **options):
        predefined_services_data = {
            'Pet Grooming': [
                {
                    'name': 'Basic Bath & Brush',
                    'description': 'Complete bath with shampoo, conditioning, blow-dry, and basic brushing. Perfect for regular maintenance.',
                    'suggested_price': 350.00,
                    'suggested_duration': 60,
                },
                {
                    'name': 'Full Grooming Package',
                    'description': 'Complete grooming service including bath, haircut, nail trimming, ear cleaning, and teeth brushing.',
                    'suggested_price': 650.00,
                    'suggested_duration': 120,
                },
                {
                    'name': 'Nail Trimming',
                    'description': 'Professional nail trimming and filing to keep your pet\'s nails healthy and comfortable.',
                    'suggested_price': 150.00,
                    'suggested_duration': 20,
                },
                {
                    'name': 'De-shedding Treatment',
                    'description': 'Specialized treatment to reduce shedding using professional tools and techniques.',
                    'suggested_price': 450.00,
                    'suggested_duration': 90,
                },
                {
                    'name': 'Flea & Tick Treatment',
                    'description': 'Specialized bath and treatment for flea and tick removal with medicated shampoo.',
                    'suggested_price': 550.00,
                    'suggested_duration': 75,
                },
            ],
            'Pet Walking': [
                {
                    'name': '30-Minute Walk',
                    'description': 'A refreshing 30-minute walk around the neighborhood to keep your dog active and happy.',
                    'suggested_price': 200.00,
                    'suggested_duration': 30,
                },
                {
                    'name': '1-Hour Adventure Walk',
                    'description': 'Extended 1-hour walk with playtime and exploration for high-energy dogs.',
                    'suggested_price': 350.00,
                    'suggested_duration': 60,
                },
                {
                    'name': 'Group Walk',
                    'description': 'Socialization walk with other friendly dogs in a supervised group setting.',
                    'suggested_price': 250.00,
                    'suggested_duration': 45,
                },
                {
                    'name': 'Puppy Walk & Training',
                    'description': 'Gentle walk combined with basic leash training for puppies and young dogs.',
                    'suggested_price': 300.00,
                    'suggested_duration': 45,
                },
            ],
            'Pet Training': [
                {
                    'name': 'Basic Obedience Training',
                    'description': 'Fundamental commands including sit, stay, come, and down. Perfect for new pet owners.',
                    'suggested_price': 750.00,
                    'suggested_duration': 60,
                },
                {
                    'name': 'Puppy Training Package',
                    'description': 'Comprehensive puppy training covering house training, socialization, and basic commands.',
                    'suggested_price': 1000.00,
                    'suggested_duration': 90,
                },
                {
                    'name': 'Behavioral Consultation',
                    'description': 'One-on-one consultation to address specific behavioral issues and create training plans.',
                    'suggested_price': 850.00,
                    'suggested_duration': 75,
                },
                {
                    'name': 'Advanced Training Session',
                    'description': 'Advanced commands and tricks for well-trained pets looking to learn more.',
                    'suggested_price': 900.00,
                    'suggested_duration': 60,
                },
            ],
            'Pet Sitting': [
                {
                    'name': 'Daily Pet Visit',
                    'description': 'Daily check-in visit including feeding, playtime, and companionship for your pet.',
                    'suggested_price': 250.00,
                    'suggested_duration': 30,
                },
                {
                    'name': 'Overnight Pet Sitting',
                    'description': 'Overnight care in your home to ensure your pet feels comfortable and secure.',
                    'suggested_price': 750.00,
                    'suggested_duration': 720,  # 12 hours
                },
                {
                    'name': 'Extended Day Care',
                    'description': 'Full day pet sitting service (8+ hours) with meals, walks, and attention.',
                    'suggested_price': 600.00,
                    'suggested_duration': 480,  # 8 hours
                },
                {
                    'name': 'Holiday Pet Care',
                    'description': 'Special holiday care package with extra attention and activities for your pet.',
                    'suggested_price': 850.00,
                    'suggested_duration': 480,
                },
            ],
            'Veterinary Care': [
                {
                    'name': 'Wellness Checkup',
                    'description': 'Comprehensive health examination including vital signs, weight check, and general assessment.',
                    'suggested_price': 650.00,
                    'suggested_duration': 45,
                },
                {
                    'name': 'Vaccination Service',
                    'description': 'Essential vaccinations to keep your pet protected against common diseases.',
                    'suggested_price': 450.00,
                    'suggested_duration': 30,
                },
                {
                    'name': 'Emergency Consultation',
                    'description': 'Urgent care consultation for immediate health concerns and emergencies.',
                    'suggested_price': 1200.00,
                    'suggested_duration': 60,
                },
                {
                    'name': 'Health Certificate',
                    'description': 'Official health certificate for travel or boarding requirements.',
                    'suggested_price': 350.00,
                    'suggested_duration': 20,
                },
            ],
            'Pet Boarding': [
                {
                    'name': 'Standard Boarding (per night)',
                    'description': 'Comfortable overnight boarding with meals, exercise, and basic care.',
                    'suggested_price': 450.00,
                    'suggested_duration': 1440,  # 24 hours
                },
                {
                    'name': 'Luxury Boarding (per night)',
                    'description': 'Premium boarding with extra amenities, playtime, and personalized attention.',
                    'suggested_price': 750.00,
                    'suggested_duration': 1440,
                },
                {
                    'name': 'Daycare Service',
                    'description': 'Daytime boarding with socialization, play, and supervised activities.',
                    'suggested_price': 350.00,
                    'suggested_duration': 480,  # 8 hours
                },
            ],
            'Pet Photography': [
                {
                    'name': 'Portrait Session',
                    'description': 'Professional pet portrait session with multiple poses and backgrounds.',
                    'suggested_price': 1500.00,
                    'suggested_duration': 90,
                },
                {
                    'name': 'Outdoor Adventure Shoot',
                    'description': 'Dynamic outdoor photography session capturing your pet in natural settings.',
                    'suggested_price': 2000.00,
                    'suggested_duration': 120,
                },
                {
                    'name': 'Family Pet Session',
                    'description': 'Family photos including pets with professional editing and digital delivery.',
                    'suggested_price': 2500.00,
                    'suggested_duration': 120,
                },
            ],
            'Pet Transportation': [
                {
                    'name': 'Vet Appointment Transport',
                    'description': 'Safe transportation to and from veterinary appointments with care and comfort.',
                    'suggested_price': 300.00,
                    'suggested_duration': 60,
                },
                {
                    'name': 'Airport Pet Transport',
                    'description': 'Specialized transportation service for airport pickups and drop-offs.',
                    'suggested_price': 650.00,
                    'suggested_duration': 90,
                },
                {
                    'name': 'Emergency Transport',
                    'description': 'Urgent transportation service for emergency veterinary care.',
                    'suggested_price': 750.00,
                    'suggested_duration': 45,
                },
            ],
        }

        created_count = 0
        updated_count = 0

        for category_name, services in predefined_services_data.items():
            try:
                category = ServiceCategory.objects.get(name=category_name)
                
                for service_data in services:
                    predefined_service, created = PredefinedService.objects.get_or_create(
                        category=category,
                        name=service_data['name'],
                        defaults={
                            'description': service_data['description'],
                            'suggested_price': service_data['suggested_price'],
                            'suggested_duration': service_data['suggested_duration'],
                        }
                    )
                    
                    if created:
                        created_count += 1
                        self.stdout.write(
                            self.style.SUCCESS(f'Created: {service_data["name"]} for {category_name}')
                        )
                    else:
                        # Update existing service if needed
                        updated = False
                        if (predefined_service.description != service_data['description'] or
                            predefined_service.suggested_price != service_data['suggested_price'] or
                            predefined_service.suggested_duration != service_data['suggested_duration']):
                            
                            predefined_service.description = service_data['description']
                            predefined_service.suggested_price = service_data['suggested_price']
                            predefined_service.suggested_duration = service_data['suggested_duration']
                            predefined_service.save()
                            updated = True
                            updated_count += 1
                        
                        if updated:
                            self.stdout.write(
                                self.style.WARNING(f'Updated: {service_data["name"]} for {category_name}')
                            )
                        else:
                            self.stdout.write(
                                self.style.SUCCESS(f'Already exists: {service_data["name"]} for {category_name}')
                            )
                            
            except ServiceCategory.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Category "{category_name}" not found. Please run create_sample_categories first.')
                )

        self.stdout.write(
            self.style.SUCCESS(
                f'\nSummary: {created_count} predefined services created, {updated_count} updated'
            )
        )
