from django.contrib import admin
from .models import (
    ProductCategory, Product, ProductImage, Review,
    Cart, CartItem, Order, OrderItem, Seller
)


class ProductImageInline(admin.TabularInline):
    model = ProductImage
    extra = 1


class ReviewInline(admin.TabularInline):
    model = Review
    extra = 0
    readonly_fields = ('user', 'rating', 'comment', 'created_at')
    can_delete = False


@admin.register(ProductCategory)
class ProductCategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'description')
    search_fields = ('name',)


@admin.register(Seller)
class SellerAdmin(admin.ModelAdmin):
    list_display = ('business_name', 'user', 'is_verified', 'is_active', 'rating', 'total_sales', 'created_at')
    list_filter = ('is_verified', 'is_active', 'created_at')
    search_fields = ('business_name', 'user__full_name', 'user__email', 'business_email')
    readonly_fields = ('rating', 'reviews_count', 'total_sales', 'created_at', 'updated_at')
    fieldsets = (
        ('Business Information', {
            'fields': ('user', 'business_name', 'business_description', 'business_address')
        }),
        ('Contact Information', {
            'fields': ('business_phone', 'business_email', 'business_website', 'business_logo')
        }),
        ('Status & Verification', {
            'fields': ('is_verified', 'is_active')
        }),
        ('Statistics', {
            'fields': ('rating', 'reviews_count', 'total_sales'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ('name', 'seller', 'category', 'pet_category', 'price', 'discount_price', 'stock', 'is_available')
    list_filter = ('category', 'pet_category', 'is_available', 'seller')
    search_fields = ('name', 'description', 'seller__business_name')
    inlines = [ProductImageInline, ReviewInline]


@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    list_display = ('product', 'user', 'rating', 'created_at')
    list_filter = ('rating', 'created_at')
    search_fields = ('product__name', 'user__username', 'comment')
    readonly_fields = ('created_at',)


class CartItemInline(admin.TabularInline):
    model = CartItem
    extra = 0


@admin.register(Cart)
class CartAdmin(admin.ModelAdmin):
    list_display = ('user', 'created_at', 'updated_at', 'get_total_items', 'get_total_price')
    search_fields = ('user__username',)
    inlines = [CartItemInline]


class OrderItemInline(admin.TabularInline):
    model = OrderItem
    extra = 0
    readonly_fields = ('product', 'quantity', 'price')


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'status', 'total_price', 'created_at')
    list_filter = ('status', 'created_at')
    search_fields = ('user__username', 'id')
    readonly_fields = ('created_at', 'updated_at')
    inlines = [OrderItemInline]
