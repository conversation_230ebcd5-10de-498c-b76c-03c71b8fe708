from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib import messages
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.db.models import Q
from django.http import JsonResponse
from datetime import datetime
from .models import (
    ServiceCategory, ServiceProvider, Service, Availability,
    Booking, ServiceReview, ServiceProviderGallery, PredefinedService
)
from .forms import (
    ServiceProviderForm, ServiceForm, AvailabilityForm,
    BookingForm, ServiceReviewForm, GalleryImageForm
)
from pets.models import PetCategory


class ServicesHomeView(ListView):
    """Main services landing page with categories and featured providers"""
    model = ServiceProvider
    template_name = 'services/services_home.html'
    context_object_name = 'featured_providers'
    paginate_by = 6

    def get_queryset(self):
        return ServiceProvider.objects.filter(
            is_available=True
        ).order_by('-rating', '-user__date_joined')[:6]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = ServiceCategory.objects.all()
        context['total_providers'] = ServiceProvider.objects.filter(is_available=True).count()
        context['total_categories'] = ServiceCategory.objects.count()
        return context


class ServiceCategoryListView(ListView):
    """View for listing service categories"""
    model = ServiceCategory
    template_name = 'services/category_list.html'
    context_object_name = 'categories'


class ServiceProviderListView(ListView):
    """View for listing service providers"""
    model = ServiceProvider
    template_name = 'services/provider_list.html'
    context_object_name = 'providers'
    paginate_by = 12

    def get_queryset(self):
        queryset = super().get_queryset().filter(is_available=True)

        # Filter by category if provided
        category = self.request.GET.get('category')
        if category:
            queryset = queryset.filter(categories__slug=category)

        # Filter by pet category if provided
        pet_category = self.request.GET.get('pet_category')
        if pet_category:
            queryset = queryset.filter(pet_categories__slug=pet_category)

        # Filter by rating if provided
        rating = self.request.GET.get('rating')
        if rating:
            queryset = queryset.filter(rating__gte=rating)

        # Search functionality
        search_query = self.request.GET.get('search')
        if search_query:
            queryset = queryset.filter(
                Q(user__full_name__icontains=search_query) |
                Q(user__email__icontains=search_query) |
                Q(bio__icontains=search_query) |
                Q(services__name__icontains=search_query)
            ).distinct()

        # Sort functionality
        sort_by = self.request.GET.get('sort_by', '-rating')
        if sort_by == 'name':
            queryset = queryset.order_by('user__full_name', 'user__email')
        elif sort_by == '-name':
            queryset = queryset.order_by('-user__full_name', '-user__email')
        elif sort_by == 'rating':
            queryset = queryset.order_by('rating')
        elif sort_by == '-rating':
            queryset = queryset.order_by('-rating')
        elif sort_by == 'newest':
            queryset = queryset.order_by('-user__date_joined')
        elif sort_by == 'oldest':
            queryset = queryset.order_by('user__date_joined')
        else:
            queryset = queryset.order_by('-rating')

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = ServiceCategory.objects.all()
        context['pet_categories'] = PetCategory.objects.all()
        context['current_category'] = self.request.GET.get('category')
        context['current_pet_category'] = self.request.GET.get('pet_category')
        context['search_query'] = self.request.GET.get('search', '')
        context['sort_by'] = self.request.GET.get('sort_by', '-rating')
        return context


class CategoryProvidersView(ListView):
    """View for listing service providers in a specific category"""
    model = ServiceProvider
    template_name = 'services/category_providers.html'
    context_object_name = 'providers'
    paginate_by = 12

    def get_queryset(self):
        self.category = get_object_or_404(ServiceCategory, slug=self.kwargs['slug'])
        return ServiceProvider.objects.filter(
            categories=self.category,
            is_available=True
        ).order_by('-rating', '-user__date_joined')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['category'] = self.category
        context['categories'] = ServiceCategory.objects.all()
        return context


class ServiceProviderDetailView(DetailView):
    """View for displaying service provider details"""
    model = ServiceProvider
    template_name = 'services/provider_detail.html'
    context_object_name = 'provider'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['services'] = self.object.services.filter(is_available=True)
        context['gallery'] = self.object.gallery.all()
        context['availability'] = self.object.availability.all().order_by('day_of_week', 'start_time')

        # Get reviews from completed bookings
        reviews = ServiceReview.objects.filter(
            booking__service__provider=self.object,
            booking__status='completed'
        ).order_by('-created_at')

        context['reviews'] = reviews

        return context


@login_required
def become_provider(request):
    """View for becoming a service provider"""
    # Check if user is already a provider
    if hasattr(request.user, 'service_provider'):
        messages.warning(request, 'You are already a service provider!')
        return redirect('provider-detail', pk=request.user.service_provider.pk)

    if request.method == 'POST':
        form = ServiceProviderForm(request.POST)
        if form.is_valid():
            provider = form.save(commit=False)
            provider.user = request.user
            provider.save()
            form.save_m2m()  # Save many-to-many relationships

            # Update user model
            request.user.is_service_provider = True
            request.user.save()

            messages.success(request, 'You are now a service provider!')
            return redirect('provider-detail', pk=provider.pk)
    else:
        form = ServiceProviderForm()

    return render(request, 'services/become_provider.html', {'form': form})


class ServiceCreateView(LoginRequiredMixin, UserPassesTestMixin, CreateView):
    """View for creating a new service"""
    model = Service
    form_class = ServiceForm
    template_name = 'services/service_form.html'

    def form_valid(self, form):
        form.instance.provider = self.request.user.service_provider

        # Set predefined_service if using predefined service
        service_type = form.cleaned_data.get('service_type')
        if service_type == 'predefined':
            form.instance.predefined_service = form.cleaned_data.get('predefined_service')

        messages.success(self.request, 'Service created successfully!')
        return super().form_valid(form)

    def test_func(self):
        return hasattr(self.request.user, 'service_provider')


class ServiceUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    """View for updating a service"""
    model = Service
    form_class = ServiceForm
    template_name = 'services/service_form.html'

    def form_valid(self, form):
        messages.success(self.request, 'Service updated successfully!')
        return super().form_valid(form)

    def test_func(self):
        service = self.get_object()
        return hasattr(self.request.user, 'service_provider') and self.request.user.service_provider == service.provider


@login_required
def add_availability(request):
    """View for adding availability slots"""
    if not hasattr(request.user, 'service_provider'):
        messages.error(request, 'You must be a service provider to add availability!')
        return redirect('become-provider')

    if request.method == 'POST':
        form = AvailabilityForm(request.POST)
        if form.is_valid():
            availability = form.save(commit=False)
            availability.provider = request.user.service_provider
            availability.save()
            messages.success(request, 'Availability added successfully!')
            return redirect('provider-detail', pk=request.user.service_provider.pk)
    else:
        form = AvailabilityForm()

    return render(request, 'services/availability_form.html', {'form': form})


@login_required
def delete_availability(request, pk):
    """View for deleting availability slots"""
    availability = get_object_or_404(Availability, pk=pk)

    if request.user.service_provider != availability.provider:
        messages.error(request, 'You can only delete your own availability slots!')
        return redirect('provider-detail', pk=request.user.service_provider.pk)

    if request.method == 'POST':
        availability.delete()
        messages.success(request, 'Availability deleted successfully!')
        return redirect('provider-detail', pk=request.user.service_provider.pk)

    return render(request, 'services/availability_confirm_delete.html', {'availability': availability})


@login_required
def book_service(request, pk):
    """View for booking a specific service"""
    print(f"Attempting to book service with ID: {pk}")

    # Debug: List all services in database
    all_services = Service.objects.all()
    print(f"Total services in database: {all_services.count()}")
    for s in all_services:
        print(f"  Service ID: {s.id}, Name: {s.name}, Provider: {s.provider.user.email}, Available: {s.is_available}")

    # Check if service exists at all
    try:
        service_check = Service.objects.get(pk=pk)
        print(f"Service found: {service_check.name}, Available: {service_check.is_available}")
    except Service.DoesNotExist:
        print(f"Service with ID {pk} does not exist")

    service = get_object_or_404(Service, pk=pk, is_available=True)

    # Check for existing pending/confirmed bookings for the same service
    existing_booking = Booking.objects.filter(
        user=request.user,
        service=service,
        status__in=['pending', 'confirmed']
    ).first()

    if existing_booking:
        messages.warning(request, f'You already have a {existing_booking.status} booking for this service.')
        return redirect('booking-detail', pk=existing_booking.pk)

    if request.method == 'POST':
        print(f"POST request received for booking service {service.name}")
        form = BookingForm(request.user, service, request.POST)
        print(f"Form data: {request.POST}")
        print(f"User pets: {request.user.pets.all()}")

        if form.is_valid():
            print("Form is valid, creating booking...")
            booking = form.save(commit=False)
            booking.user = request.user
            booking.service = service
            booking.end_time = form.cleaned_data['end_time']
            booking.total_price = form.cleaned_data['total_price']
            booking.save()
            print(f"Booking created: {booking}")

            # Create notification for service provider
            from messaging.models import Notification
            Notification.objects.create(
                recipient=service.provider.user,
                sender=request.user,
                notification_type='service_booking',
                message=f"{request.user.full_name or request.user.email} booked your service: {service.name}"
            )

            messages.success(request, 'Service booked successfully! Please wait for confirmation.')
            return redirect('booking-detail', pk=booking.pk)
        else:
            print(f"Form is not valid. Errors: {form.errors}")
            print(f"Non-field errors: {form.non_field_errors()}")
    else:
        form = BookingForm(request.user, service)

    return render(request, 'services/booking_form.html', {
        'form': form,
        'service': service
    })


@login_required
def book_provider(request, pk):
    """View for booking a provider (when they have multiple services)"""
    provider = get_object_or_404(ServiceProvider, pk=pk, is_available=True)
    services = provider.services.filter(is_available=True)

    if not services.exists():
        messages.error(request, 'This provider has no available services.')
        return redirect('provider-detail', pk=pk)

    if services.count() == 1:
        # If only one service, redirect to direct service booking
        return redirect('book-service', pk=services.first().pk)

    if request.method == 'POST':
        service_id = request.POST.get('service')
        if service_id:
            try:
                service = services.get(pk=service_id)
                return redirect('book-service', pk=service.pk)
            except Service.DoesNotExist:
                messages.error(request, 'Invalid service selected.')

    return render(request, 'services/book_provider.html', {
        'provider': provider,
        'services': services
    })


@login_required
def booking_list(request):
    """View for listing user's bookings"""
    bookings = Booking.objects.filter(user=request.user).order_by('-date', '-start_time')
    return render(request, 'services/booking_list.html', {'bookings': bookings})


@login_required
def booking_detail(request, pk):
    """View for displaying booking details"""
    booking = get_object_or_404(Booking, pk=pk)

    # Check if user is authorized to view this booking
    if booking.user != request.user and booking.service.provider.user != request.user:
        messages.error(request, 'You are not authorized to view this booking!')
        return redirect('booking-list')

    # Check if user can review this booking
    can_review = False
    if booking.user == request.user and booking.status == 'completed' and not hasattr(booking, 'review'):
        can_review = True

    review_form = ServiceReviewForm() if can_review else None

    return render(request, 'services/booking_detail.html', {
        'booking': booking,
        'can_review': can_review,
        'review_form': review_form
    })


@login_required
def update_booking_status(request, pk):
    """View for updating booking status (for providers)"""
    booking = get_object_or_404(Booking, pk=pk)

    # Check if user is the service provider
    if not hasattr(request.user, 'service_provider') or request.user.service_provider != booking.service.provider:
        messages.error(request, 'You are not authorized to update this booking!')
        return redirect('booking-list')

    if request.method == 'POST':
        status = request.POST.get('status')
        if status in ['confirmed', 'completed', 'cancelled']:
            booking.status = status
            booking.save()

            # Create notification for customer
            from messaging.models import Notification
            try:
                status_messages = {
                    'confirmed': f"Your booking for {booking.service.name} has been confirmed!",
                    'completed': f"Your booking for {booking.service.name} has been marked as completed.",
                    'cancelled': f"Your booking for {booking.service.name} has been cancelled by the provider."
                }

                if status in status_messages:
                    Notification.objects.create(
                        recipient=booking.user,
                        sender=request.user,
                        notification_type=f'booking_{status}',
                        message=status_messages[status]
                    )
            except Exception as e:
                print(f"Failed to create notification: {e}")

            messages.success(request, f'Booking status updated to {status}!')
        else:
            messages.error(request, 'Invalid status!')

    # Check if request came from provider dashboard
    referer = request.META.get('HTTP_REFERER', '')
    if 'dashboard/bookings' in referer:
        return redirect('provider-bookings')
    else:
        return redirect('booking-detail', pk=pk)


@login_required
def cancel_booking(request, pk):
    """View for cancelling a booking (AJAX endpoint)"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Invalid request method'})

    booking = get_object_or_404(Booking, pk=pk)

    # Check if user is authorized to cancel this booking
    if booking.user != request.user:
        return JsonResponse({'success': False, 'error': 'You are not authorized to cancel this booking!'})

    # Check if booking can be cancelled
    if booking.status not in ['pending', 'confirmed']:
        return JsonResponse({'success': False, 'error': 'This booking cannot be cancelled!'})

    # Cancel the booking
    booking.status = 'cancelled'
    booking.save()

    # Create notification for service provider
    from messaging.models import Notification
    try:
        Notification.objects.create(
            recipient=booking.service.provider.user,
            sender=request.user,
            notification_type='booking_cancelled',
            message=f"{request.user.full_name or request.user.email} cancelled their booking for: {booking.service.name}"
        )
    except Exception as e:
        # Log the error but don't fail the cancellation
        print(f"Failed to create notification: {e}")

    return JsonResponse({'success': True, 'message': 'Booking cancelled successfully!'})


@login_required
def add_service_review(request, pk):
    """View for adding a review for a completed service"""
    booking = get_object_or_404(Booking, pk=pk, status='completed')

    # Check if user is authorized to review this booking
    if booking.user != request.user:
        messages.error(request, 'You can only review your own bookings!')
        return redirect('booking-detail', pk=pk)

    # Check if booking already has a review
    if hasattr(booking, 'review'):
        messages.error(request, 'You have already reviewed this service!')
        return redirect('booking-detail', pk=pk)

    if request.method == 'POST':
        form = ServiceReviewForm(request.POST)
        if form.is_valid():
            review = form.save(commit=False)
            review.booking = booking
            review.save()

            messages.success(request, 'Review added successfully!')
            return redirect('booking-detail', pk=pk)
    else:
        form = ServiceReviewForm()

    return render(request, 'services/review_form.html', {
        'form': form,
        'booking': booking
    })


# Provider Dashboard Views
@login_required
def provider_dashboard(request):
    """View for service provider dashboard"""
    if not hasattr(request.user, 'service_provider'):
        messages.error(request, 'You must be a service provider to access the dashboard!')
        return redirect('become-provider')

    provider = request.user.service_provider

    # Get counts for dashboard
    services_count = Service.objects.filter(provider=provider).count()
    pending_bookings_count = Booking.objects.filter(
        service__provider=provider,
        status='pending'
    ).count()
    completed_bookings_count = Booking.objects.filter(
        service__provider=provider,
        status='completed'
    ).count()

    # Get recent bookings
    recent_bookings = Booking.objects.filter(
        service__provider=provider
    ).order_by('-date', '-start_time')[:5]

    context = {
        'active_tab': 'dashboard',
        'services_count': services_count,
        'pending_bookings_count': pending_bookings_count,
        'completed_bookings_count': completed_bookings_count,
        'recent_bookings': recent_bookings,
    }

    return render(request, 'services/provider_dashboard.html', context)


@login_required
def provider_services(request):
    """View for managing provider services"""
    if not hasattr(request.user, 'service_provider'):
        messages.error(request, 'You must be a service provider to access this page!')
        return redirect('become-provider')

    provider = request.user.service_provider
    services = Service.objects.filter(provider=provider).order_by('-is_available', 'name')

    context = {
        'active_tab': 'services',
        'services': services,
    }

    return render(request, 'services/provider_services.html', context)


@login_required
def provider_bookings(request):
    """View for managing provider bookings"""
    if not hasattr(request.user, 'service_provider'):
        messages.error(request, 'You must be a service provider to access this page!')
        return redirect('become-provider')

    provider = request.user.service_provider
    booking_filter = request.GET.get('filter', 'all')

    # Filter bookings based on status
    if booking_filter == 'pending':
        bookings = Booking.objects.filter(service__provider=provider, status='pending')
    elif booking_filter == 'confirmed':
        bookings = Booking.objects.filter(service__provider=provider, status='confirmed')
    elif booking_filter == 'completed':
        bookings = Booking.objects.filter(service__provider=provider, status='completed')
    elif booking_filter == 'cancelled':
        bookings = Booking.objects.filter(service__provider=provider, status='cancelled')
    else:
        bookings = Booking.objects.filter(service__provider=provider)

    # Order bookings by date and time
    bookings = bookings.order_by('-date', '-start_time')

    context = {
        'active_tab': 'bookings',
        'bookings': bookings,
        'booking_filter': booking_filter,
    }

    return render(request, 'services/provider_bookings.html', context)


@login_required
def provider_availability(request):
    """View for managing provider availability"""
    if not hasattr(request.user, 'service_provider'):
        messages.error(request, 'You must be a service provider to access this page!')
        return redirect('become-provider')

    provider = request.user.service_provider

    # Get all availability slots
    availability_slots = Availability.objects.filter(provider=provider).order_by('day_of_week', 'start_time')

    # Organize availability by day
    days_of_week = {
        0: 'Monday',
        1: 'Tuesday',
        2: 'Wednesday',
        3: 'Thursday',
        4: 'Friday',
        5: 'Saturday',
        6: 'Sunday',
    }

    availability_by_day = {}
    for day_num, day_name in days_of_week.items():
        day_slots = availability_slots.filter(day_of_week=day_num)

        # Organize by time of day
        morning_slots = day_slots.filter(start_time__lt=datetime.strptime('12:00', '%H:%M').time())
        afternoon_slots = day_slots.filter(
            start_time__gte=datetime.strptime('12:00', '%H:%M').time(),
            start_time__lt=datetime.strptime('17:00', '%H:%M').time()
        )
        evening_slots = day_slots.filter(start_time__gte=datetime.strptime('17:00', '%H:%M').time())

        availability_by_day[day_name] = {
            'morning': morning_slots,
            'afternoon': afternoon_slots,
            'evening': evening_slots,
        }

    # Handle form submission for adding availability
    if request.method == 'POST':
        availability_form = AvailabilityForm(request.POST)
        if availability_form.is_valid():
            availability = availability_form.save(commit=False)
            availability.provider = provider
            availability.save()
            messages.success(request, 'Availability added successfully!')
            return redirect('provider-availability')
    else:
        availability_form = AvailabilityForm()

    context = {
        'active_tab': 'availability',
        'availability_by_day': availability_by_day,
        'availability_form': availability_form,
    }

    return render(request, 'services/provider_availability.html', context)


@login_required
def provider_settings(request):
    """View for managing provider settings"""
    if not hasattr(request.user, 'service_provider'):
        messages.error(request, 'You must be a service provider to access this page!')
        return redirect('become-provider')

    provider = request.user.service_provider

    if request.method == 'POST':
        form = ServiceProviderForm(request.POST, request.FILES, instance=provider)
        if form.is_valid():
            provider = form.save(commit=False)

            # Handle profile picture if provided
            if 'profile_picture' in request.FILES:
                provider.profile_picture = request.FILES['profile_picture']

            provider.save()
            form.save_m2m()  # Save many-to-many relationships

            messages.success(request, 'Provider settings updated successfully!')
            return redirect('provider-settings')
    else:
        form = ServiceProviderForm(instance=provider)

    # Get gallery images
    gallery_images = ServiceProviderGallery.objects.filter(provider=provider)

    # Gallery image form
    gallery_form = GalleryImageForm()

    context = {
        'active_tab': 'settings',
        'form': form,
        'gallery_images': gallery_images,
        'gallery_form': gallery_form,
    }

    return render(request, 'services/provider_settings.html', context)


@login_required
def provider_toggle_availability(request):
    """AJAX view for toggling provider availability"""
    if not hasattr(request.user, 'service_provider'):
        return JsonResponse({'success': False, 'error': 'Not a service provider'})

    if request.method == 'POST':
        import json
        data = json.loads(request.body)
        is_available = data.get('is_available', False)

        provider = request.user.service_provider
        provider.is_available = is_available
        provider.save()

        return JsonResponse({'success': True})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


class ServiceDeleteView(LoginRequiredMixin, UserPassesTestMixin, DeleteView):
    """View for deleting a service"""
    model = Service
    template_name = 'services/service_confirm_delete.html'

    def test_func(self):
        service = self.get_object()
        return hasattr(self.request.user, 'service_provider') and self.request.user.service_provider == service.provider

    def get_success_url(self):
        return reverse_lazy('provider-detail', kwargs={'pk': self.object.provider.pk})

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Service deleted successfully!')
        return super().delete(request, *args, **kwargs)


@login_required
def add_gallery_image(request):
    """View for adding gallery images"""
    if not hasattr(request.user, 'service_provider'):
        messages.error(request, 'You must be a service provider to add gallery images!')
        return redirect('become-provider')

    if request.method == 'POST':
        image = request.FILES.get('image')
        caption = request.POST.get('caption', '')

        if image:
            gallery_image = ServiceProviderGallery(
                provider=request.user.service_provider,
                image=image,
                caption=caption
            )
            gallery_image.save()
            messages.success(request, 'Gallery image added successfully!')
        else:
            messages.error(request, 'Please select an image to upload!')

        return redirect('provider-settings')

    return redirect('provider-settings')


@login_required
def delete_gallery_image(request, pk):
    """View for deleting gallery images"""
    gallery_image = get_object_or_404(ServiceProviderGallery, pk=pk)

    if request.user.service_provider != gallery_image.provider:
        messages.error(request, 'You can only delete your own gallery images!')
        return redirect('provider-settings')

    if request.method == 'POST':
        gallery_image.delete()
        messages.success(request, 'Gallery image deleted successfully!')

    return redirect('provider-settings')


def get_predefined_services(request):
    """API endpoint to get predefined services by category"""
    category_id = request.GET.get('category_id')

    if category_id:
        services = PredefinedService.objects.filter(
            category_id=category_id,
            is_active=True
        ).values('id', 'name', 'description', 'suggested_price', 'suggested_duration')
    else:
        services = PredefinedService.objects.filter(
            is_active=True
        ).values('id', 'name', 'description', 'suggested_price', 'suggested_duration', 'category__name')

    return JsonResponse({'services': list(services)})


def debug_services(request):
    """Debug view to check what services exist"""
    from django.http import HttpResponse

    html = "<h1>Services Debug</h1>"

    # Check all services
    all_services = Service.objects.all()
    html += f"<h2>Total services: {all_services.count()}</h2>"

    if all_services.exists():
        html += "<ul>"
        for service in all_services:
            html += f"<li>"
            html += f"<strong>ID: {service.id}</strong> - {service.name}<br>"
            html += f"Provider: {service.provider.user.email}<br>"
            html += f"Available: {service.is_available}<br>"
            html += f"Price: ${service.price}<br>"
            html += f"Duration: {service.duration} minutes<br>"
            html += f"<a href='/services/book-service/{service.id}/'>Book This Service</a><br>"
            html += "</li><br>"
        html += "</ul>"
    else:
        html += "<p>No services found in database!</p>"

    # Check all providers
    providers = ServiceProvider.objects.all()
    html += f"<h2>Total providers: {providers.count()}</h2>"

    if providers.exists():
        html += "<ul>"
        for provider in providers:
            html += f"<li>"
            html += f"<strong>Provider ID: {provider.id}</strong> - {provider.user.email}<br>"
            html += f"Services count: {provider.services.count()}<br>"
            html += f"<a href='/services/providers/{provider.id}/'>View Provider</a><br>"
            html += "</li><br>"
        html += "</ul>"

    return HttpResponse(html)