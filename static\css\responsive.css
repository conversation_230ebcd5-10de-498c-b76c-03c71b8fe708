/* Responsive Styles */

/* Small devices (landscape phones, 576px and up) */
@media (max-width: 576px) {
  html {
    font-size: 14px;
  }

  h1 {
    font-size: var(--font-3xl);
  }

  h2 {
    font-size: var(--font-2xl);
  }

  h3 {
    font-size: var(--font-xl);
  }

  .container {
    padding: 0 var(--spacing-sm);
  }

  .main-nav {
    display: none !important;
  }

  .mobile-menu-toggle {
    display: block !important;
  }

  .search-bar {
    display: none;
  }

  .header-content {
    justify-content: space-between;
  }

  .footer-content {
    grid-template-columns: var(--grid-cols-1);
    gap: var(--gap-xl);
  }

  .footer-section {
    text-align: center;
  }

  .hero-content {
    flex-direction: column;
    text-align: center;
  }

  .hero-text {
    order: 2;
  }

  .hero-image {
    order: 1;
    margin-bottom: var(--spacing-xl);
  }

  .hero-buttons {
    flex-direction: column;
    gap: var(--spacing-base);
  }

  .features-grid {
    grid-template-columns: var(--grid-cols-1);
  }

  .category-cards {
    grid-template-columns: var(--grid-cols-1);
  }

  .product-slider {
    margin: 0 -var(--spacing-sm);
  }

  .testimonial-slider {
    margin: 0 -var(--spacing-sm);
  }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 577px) and (max-width: 768px) {
  .footer-content {
    grid-template-columns: var(--grid-cols-2);
    gap: var(--gap-xl);
  }

  .hero-content {
    flex-direction: column;
    text-align: center;
  }

  .hero-image {
    margin-top: var(--spacing-xl);
  }

  .features-grid {
    grid-template-columns: var(--grid-cols-2);
  }

  .category-cards {
    grid-template-columns: var(--grid-cols-2);
  }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 769px) and (max-width: 992px) {
  .search-bar {
    flex-basis: 40%;
  }

  .footer-content {
    grid-template-columns: var(--grid-cols-3);
    gap: var(--gap-xl);
  }

  .features-grid {
    grid-template-columns: var(--grid-cols-2);
  }

  .category-cards {
    grid-template-columns: var(--grid-cols-3);
  }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 993px) {
  .search-bar {
    flex-basis: 30%;
  }

  .features-grid {
    grid-template-columns: var(--grid-cols-4);
  }

  .category-cards {
    grid-template-columns: var(--grid-cols-4);
  }
}

/* Mobile Menu Styles */
@media (max-width: 768px) {
  .mobile-menu-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-base);
    border-bottom: 1px solid var(--gray-200);
  }

  .mobile-menu-close {
    background: none;
    border: none;
    font-size: var(--font-xl);
    color: var(--text);
    cursor: pointer;
  }

  .mobile-search {
    padding: var(--spacing-base);
    border-bottom: 1px solid var(--gray-200);
  }

  .mobile-search form {
    display: flex;
    gap: var(--spacing-xs);
  }

  .mobile-search input {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-base);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
  }

  .mobile-search button {
    background-color: var(--primary);
    color: var(--white);
    border: none;
    border-radius: var(--radius-md);
    padding: 0 var(--spacing-base);
    cursor: pointer;
  }

  .mobile-nav {
    padding: var(--spacing-base);
  }

  .mobile-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .mobile-nav li {
    margin-bottom: var(--spacing-sm);
  }

  .mobile-nav a {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-base);
    color: var(--text);
    border-radius: var(--radius-md);
    font-weight: var(--fw-medium);
  }

  .mobile-nav a:hover {
    background-color: var(--gray-100);
  }

  .mobile-user-actions {
    padding: var(--spacing-base);
    border-top: 1px solid var(--gray-200);
  }
}

/* Django Messages Container */
.django-messages-container {
  position: fixed;
  top: var(--spacing-xl);
  right: var(--spacing-xl);
  z-index: var(--z-50);
  max-width: 300px;
}

.django-message {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-base);
  margin-bottom: var(--spacing-sm);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  animation: slideIn 0.3s ease-in-out;
}

.django-message-content {
  flex: 1;
}

.django-message-close {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  font-size: var(--font-lg);
  opacity: 0.7;
  transition: var(--transition-base);
}

.django-message-close:hover {
  opacity: 1;
}

.django-message.success {
  background-color: var(--success);
  color: var(--white);
}

.django-message.error {
  background-color: var(--danger);
  color: var(--white);
}

.django-message.warning {
  background-color: var(--warning);
  color: var(--text);
}

.django-message.info {
  background-color: var(--info);
  color: var(--white);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Hide notification-related Django messages */
.django-message:has(.django-message-content:contains("Message from")),
.django-message:has(.django-message-content:contains("Conversation")),
.django-message:has(.django-message-content:contains("sent you a message")),
.django-message:has(.django-message-content:contains("interested in")),
.django-message:has(.django-message-content:contains("responded to your inquiry")) {
  display: none !important;
}

/* Alternative approach using attribute selectors for broader compatibility */
.django-message[data-hide="true"] {
  display: none !important;
}
