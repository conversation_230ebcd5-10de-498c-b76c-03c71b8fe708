# Generated by Django 4.2.7 on 2025-05-27 19:36

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('services', '0004_servicecategory_icon_class_servicecategory_slug_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PredefinedService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('suggested_price', models.DecimalField(decimal_places=2, help_text='Suggested price range', max_digits=10)),
                ('suggested_duration', models.PositiveIntegerField(help_text='Suggested duration in minutes')),
                ('is_active', models.BooleanField(default=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='predefined_services', to='services.servicecategory')),
            ],
            options={
                'verbose_name_plural': 'Predefined Services',
                'unique_together': {('category', 'name')},
            },
        ),
        migrations.AddField(
            model_name='service',
            name='predefined_service',
            field=models.ForeignKey(blank=True, help_text='Based on predefined service template', null=True, on_delete=django.db.models.deletion.SET_NULL, to='services.predefinedservice'),
        ),
    ]
