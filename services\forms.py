from django import forms
from django.core.exceptions import ValidationError
from django.db import models
from datetime import datetime, timedelta
from .models import ServiceProvider, Service, Availability, Booking, ServiceReview


class ServiceProviderForm(forms.ModelForm):
    """Form for creating/updating service provider profile"""
    profile_picture = forms.ImageField(required=False, widget=forms.FileInput(attrs={'class': 'form-control'}))
    qualifications = forms.CharField(required=False, widget=forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}))

    class Meta:
        model = ServiceProvider
        fields = ('categories', 'pet_categories', 'bio', 'experience_years', 'hourly_rate', 'is_available')
        widgets = {
            'bio': forms.Textarea(attrs={'rows': 4}),
            'categories': forms.CheckboxSelectMultiple(),
            'pet_categories': forms.CheckboxSelectMultiple(),
        }


class ServiceForm(forms.ModelForm):
    """Form for creating/updating services"""
    SERVICE_TYPE_CHOICES = [
        ('predefined', 'Choose from predefined services'),
        ('custom', 'Create custom service'),
    ]

    service_type = forms.ChoiceField(
        choices=SERVICE_TYPE_CHOICES,
        widget=forms.RadioSelect,
        initial='predefined',
        required=True,
        label='Service Type'
    )

    predefined_service = forms.ModelChoiceField(
        queryset=None,  # Will be set in __init__
        required=False,
        empty_label="Select a service...",
        widget=forms.Select(attrs={'class': 'form-control'}),
        label='Predefined Service'
    )

    class Meta:
        model = Service
        fields = ('category', 'name', 'description', 'price', 'duration', 'is_available')
        widgets = {
            'description': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'duration': forms.NumberInput(attrs={'class': 'form-control'}),
            'category': forms.Select(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Set predefined service queryset
        from .models import PredefinedService
        self.fields['predefined_service'].queryset = PredefinedService.objects.filter(is_active=True).select_related('category')

        # Add CSS classes and placeholders
        self.fields['name'].widget.attrs.update({
            'placeholder': 'Enter service name'
        })
        self.fields['description'].widget.attrs.update({
            'placeholder': 'Describe your service in detail'
        })
        self.fields['price'].widget.attrs.update({
            'placeholder': '0.00'
        })
        self.fields['duration'].widget.attrs.update({
            'placeholder': 'Duration in minutes'
        })

    def clean(self):
        cleaned_data = super().clean()
        service_type = cleaned_data.get('service_type')
        predefined_service = cleaned_data.get('predefined_service')

        if service_type == 'predefined':
            if not predefined_service:
                raise ValidationError('Please select a predefined service.')

            # Auto-fill fields from predefined service
            cleaned_data['name'] = predefined_service.name
            cleaned_data['description'] = predefined_service.description
            cleaned_data['category'] = predefined_service.category
            cleaned_data['price'] = predefined_service.suggested_price
            cleaned_data['duration'] = predefined_service.suggested_duration

        elif service_type == 'custom':
            # Validate required fields for custom service
            required_fields = ['name', 'description', 'category', 'price', 'duration']
            for field in required_fields:
                if not cleaned_data.get(field):
                    raise ValidationError(f'{field.replace("_", " ").title()} is required for custom services.')

        return cleaned_data


class AvailabilityForm(forms.ModelForm):
    """Form for adding/updating availability"""
    class Meta:
        model = Availability
        fields = ('day_of_week', 'start_time', 'end_time')
        widgets = {
            'start_time': forms.TimeInput(attrs={'type': 'time'}),
            'end_time': forms.TimeInput(attrs={'type': 'time'}),
        }

    def clean(self):
        cleaned_data = super().clean()
        start_time = cleaned_data.get('start_time')
        end_time = cleaned_data.get('end_time')

        if start_time and end_time and start_time >= end_time:
            raise ValidationError("End time must be after start time.")

        return cleaned_data


class BookingForm(forms.ModelForm):
    """Form for creating a booking"""
    class Meta:
        model = Booking
        fields = ('pet', 'date', 'start_time', 'notes')
        widgets = {
            'date': forms.DateInput(attrs={'type': 'date'}),
            'start_time': forms.TimeInput(attrs={'type': 'time'}),
            'notes': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, user, service, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user = user
        self.service = service

        # Only show pets owned by the user
        self.fields['pet'].queryset = user.pets.all()

        # Set minimum date to today
        self.fields['date'].widget.attrs['min'] = datetime.now().strftime('%Y-%m-%d')

        # Set maximum date to 30 days from now
        max_date = datetime.now() + timedelta(days=30)
        self.fields['date'].widget.attrs['max'] = max_date.strftime('%Y-%m-%d')

    def clean(self):
        cleaned_data = super().clean()
        date = cleaned_data.get('date')
        start_time = cleaned_data.get('start_time')

        if date and start_time:
            # Calculate end time based on service duration
            start_datetime = datetime.combine(date, start_time)
            end_datetime = start_datetime + timedelta(minutes=self.service.duration)
            end_time = end_datetime.time()

            # Check if the provider is available at this time
            day_of_week = date.weekday()
            provider_availability = Availability.objects.filter(
                provider=self.service.provider,
                day_of_week=day_of_week,
                start_time__lte=start_time,
                end_time__gte=end_time
            )

            if not provider_availability.exists():
                raise ValidationError("The service provider is not available at this time.")

            # Check for overlapping bookings
            overlapping_bookings = Booking.objects.filter(
                service__provider=self.service.provider,
                date=date,
                status__in=['pending', 'confirmed'],
            ).filter(
                (models.Q(start_time__lt=end_time) & models.Q(end_time__gt=start_time))
            )

            if overlapping_bookings.exists():
                raise ValidationError("This time slot is already booked.")

            # Add end_time to cleaned_data
            cleaned_data['end_time'] = end_time

            # Calculate total price
            cleaned_data['total_price'] = self.service.price

        return cleaned_data


class ServiceReviewForm(forms.ModelForm):
    """Form for reviewing a service"""
    class Meta:
        model = ServiceReview
        fields = ('rating', 'comment')
        widgets = {
            'comment': forms.Textarea(attrs={'rows': 4}),
        }


class GalleryImageForm(forms.Form):
    """Form for adding gallery images"""
    image = forms.ImageField(
        required=True,
        widget=forms.FileInput(attrs={'class': 'form-control'})
    )
    caption = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Optional caption'})
    )
