{% extends 'base.html' %}
{% load static %}

{% block title %}Orders - Seller Dashboard{% endblock %}

{% block content %}
<div class="container">
    <div class="seller-dashboard">
        <div class="dashboard-sidebar">
            {% include 'components/seller_sidebar.html' %}
        </div>

        <div class="dashboard-main">
            <div class="dashboard-header">
                <h1>Orders</h1>
                <p>Manage orders for your products</p>
            </div>

            {% if order_items %}
                <div class="orders-section">
                    <div class="orders-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>Order ID</th>
                                    <th>Product</th>
                                    <th>Customer</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Total</th>
                                    <th>Order Date</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order_item in order_items %}
                                <tr>
                                    <td>
                                        <strong>#{{ order_item.order.id }}</strong>
                                    </td>
                                    <td>
                                        <div class="product-info">
                                            <img src="{{ order_item.product.image.url }}" alt="{{ order_item.product.name }}" class="product-thumb">
                                            <div>
                                                <div class="product-name">{{ order_item.product.name }}</div>
                                                <div class="product-category">{{ order_item.product.category.name }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="customer-info">
                                            <div class="customer-name">{{ order_item.order.user.full_name|default:order_item.order.user.email }}</div>
                                            <div class="customer-email">{{ order_item.order.user.email }}</div>
                                        </div>
                                    </td>
                                    <td>{{ order_item.quantity }}</td>
                                    <td>${{ order_item.price }}</td>
                                    <td><strong>${{ order_item.get_total_price }}</strong></td>
                                    <td>{{ order_item.order.created_at|date:"M d, Y" }}</td>
                                    <td>
                                        <span class="status-badge status-{{ order_item.order.status }}">
                                            {{ order_item.order.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="order-actions">
                                            <a href="{% url 'order-detail' pk=order_item.order.pk %}" class="btn btn-sm btn-outline">
                                                <i class="fas fa-eye"></i>
                                                View
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            {% else %}
                <div class="empty-state">
                    <i class="fas fa-shopping-cart"></i>
                    <h3>No orders yet</h3>
                    <p>Orders for your products will appear here once customers start purchasing.</p>
                    <a href="{% url 'seller-products' %}" class="btn btn-primary">
                        <i class="fas fa-boxes"></i>
                        Manage Products
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.seller-dashboard {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: var(--spacing-xl);
    padding: var(--spacing-xl) 0;
}

.dashboard-main {
    min-height: 600px;
}

.dashboard-header {
    margin-bottom: var(--spacing-2xl);
}

.dashboard-header h1 {
    color: var(--primary);
    margin-bottom: var(--spacing-xs);
}

.dashboard-header p {
    color: var(--text-muted);
    font-size: var(--font-lg);
}

.orders-section {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.orders-table {
    overflow-x: auto;
}

.orders-table table {
    width: 100%;
    border-collapse: collapse;
    min-width: 1000px;
}

.orders-table th,
.orders-table td {
    padding: var(--spacing-base);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.orders-table th {
    background: var(--gray-50);
    font-weight: var(--fw-semibold);
    color: var(--text);
    font-size: var(--font-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.orders-table tbody tr:hover {
    background: var(--gray-50);
}

.product-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.product-thumb {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.product-name {
    font-weight: var(--fw-medium);
    color: var(--text);
    font-size: var(--font-sm);
}

.product-category {
    color: var(--text-muted);
    font-size: var(--font-xs);
    text-transform: uppercase;
}

.customer-info {
    min-width: 150px;
}

.customer-name {
    font-weight: var(--fw-medium);
    color: var(--text);
    font-size: var(--font-sm);
}

.customer-email {
    color: var(--text-muted);
    font-size: var(--font-xs);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: var(--font-xs);
    font-weight: var(--fw-medium);
    text-transform: uppercase;
    white-space: nowrap;
}

.status-pending {
    background: var(--warning-light);
    color: var(--warning-dark);
}

.status-processing {
    background: var(--info-light);
    color: var(--info-dark);
}

.status-shipped {
    background: var(--primary-light);
    color: var(--primary-dark);
}

.status-delivered {
    background: var(--success-light);
    color: var(--success-dark);
}

.status-cancelled {
    background: var(--danger-light);
    color: var(--danger-dark);
}

.order-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--font-xs);
    font-weight: var(--fw-medium);
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-base);
    white-space: nowrap;
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-xs);
}

.btn-outline {
    background: transparent;
    border: 1px solid var(--primary);
    color: var(--primary);
}

.btn-outline:hover {
    background: var(--primary);
    color: var(--white);
}

.btn-primary {
    background-color: var(--primary);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
}

.empty-state {
    text-align: center;
    padding: var(--spacing-4xl);
    color: var(--text-muted);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.empty-state h3 {
    color: var(--text);
    margin-bottom: var(--spacing-base);
}

.empty-state p {
    margin-bottom: var(--spacing-xl);
    font-size: var(--font-lg);
}

@media (max-width: 992px) {
    .seller-dashboard {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .orders-table {
        font-size: var(--font-sm);
    }
    
    .orders-table th,
    .orders-table td {
        padding: var(--spacing-sm);
    }
    
    .product-info {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
    
    .product-thumb {
        width: 30px;
        height: 30px;
    }
}
</style>
{% endblock %}
