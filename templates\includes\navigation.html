{% load static %}

<!-- Main Header Navigation -->
<header class="main-header">
    <div class="container">
        <div class="header-content">
            <div class="logo">
                <a href="{% url 'home' %}">
                    <img src="{% static 'img/logo.svg' %}" alt="PetPaw Logo">
                    <span>PetPaw</span>
                </a>
            </div>

            <div class="search-bar">
                <form action="{% url 'product-list' %}" method="GET">
                    <div class="input-with-icon">
                        <input type="text" name="search" class="search-input" placeholder="Search for pets, products, services...">
                        <button type="submit" class="search-button"><i class="fas fa-search"></i></button>
                    </div>
                </form>
            </div>

            <nav class="main-nav">
                <ul>
                    <li><a href="{% url 'home' %}" {% if request.path == '/' %}class="active"{% endif %}><i class="fas fa-home"></i> Home</a></li>
                    <li><a href="{% url 'pet-list' %}" {% if '/pets/' in request.path %}class="active"{% endif %}><i class="fas fa-paw"></i> Pets</a></li>
                    <li><a href="{% url 'product-list' %}" {% if '/shop/' in request.path %}class="active"{% endif %}><i class="fas fa-shopping-bag"></i> Shop</a></li>
                    <li><a href="{% url 'services-home' %}" {% if '/services/' in request.path %}class="active"{% endif %}><i class="fas fa-concierge-bell"></i> Services</a></li>
                </ul>
            </nav>

            <div class="user-actions">
                {% if user.is_authenticated %}
                    <a href="{% url 'cart' %}" class="cart-icon">
                        <i class="fas fa-shopping-cart"></i>
                        {% if cart %}
                            <span class="cart-count">{{ cart.get_total_items }}</span>
                        {% endif %}
                    </a>
                    <a href="{% url 'notifications' %}" class="notification-icon">
                        <i class="fas fa-bell"></i>
                        {% if unread_notifications_count %}
                            <span class="notification-count">{{ unread_notifications_count }}</span>
                        {% endif %}
                    </a>
                    <a href="{% url 'inbox' %}" class="message-icon">
                        <i class="fas fa-envelope"></i>
                        {% if unread_messages_count %}
                            <span class="message-count">{{ unread_messages_count }}</span>
                        {% endif %}
                    </a>
                    <div class="user-dropdown">
                        <button class="dropdown-toggle">
                            <img src="{{ user.profile_picture.url }}" alt="{{ user.full_name|default:user.email }}" class="avatar">
                        </button>
                        <div class="dropdown-menu">
                            <a href="{% url 'user-profile' user_id=user.id %}">
                                <i class="fas fa-user"></i> My Profile
                            </a>
                            <a href="{% url 'my-pets' %}">
                                <i class="fas fa-paw"></i> My Pets
                            </a>
                            <a href="{% url 'pet-create' %}">
                                <i class="fas fa-plus-circle"></i> Add Pet
                            </a>
                            <a href="{% url 'order-list' %}">
                                <i class="fas fa-box"></i> My Orders
                            </a>
                            <a href="{% url 'booking-list' %}">
                                <i class="fas fa-calendar-check"></i> My Bookings
                            </a>
                            {% if user.is_service_provider %}
                            <a href="{% url 'provider-dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i> Provider Dashboard
                            </a>
                            {% else %}
                            <a href="{% url 'become-provider' %}">
                                <i class="fas fa-user-tie"></i> Become a Provider
                            </a>
                            {% endif %}
                            {% if user.is_seller %}
                            <a href="{% url 'seller-dashboard' %}">
                                <i class="fas fa-store"></i> Seller Dashboard
                            </a>
                            {% else %}
                            <a href="{% url 'become-seller' %}">
                                <i class="fas fa-shopping-bag"></i> Become a Seller
                            </a>
                            {% endif %}
                            <a href="/users/profile/edit/">
                                <i class="fas fa-cog"></i> Settings
                            </a>
                            <a href="{% url 'account_logout' %}">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </div>
                    </div>
                {% else %}
                    <a href="{% url 'account_login' %}" class="btn btn-primary btn-sm">Login</a>
                    <a href="{% url 'account_signup' %}" class="btn btn-secondary btn-sm">Sign Up</a>
                {% endif %}
            </div>

            <button class="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </div>
</header>

<!-- Mobile Menu -->
<div class="mobile-menu">
    <div class="mobile-menu-header">
        <div class="logo">
            <a href="{% url 'home' %}">
                <img src="{% static 'img/logo.svg' %}" alt="PetPaw Logo">
                <span>PetPaw</span>
            </a>
        </div>
        <button class="mobile-menu-close">
            <i class="fas fa-times"></i>
        </button>
    </div>
    <div class="mobile-search">
        <form action="{% url 'product-list' %}" method="GET">
            <div class="input-with-icon">
                <input type="text" name="search" class="search-input" placeholder="Search...">
                <button type="submit" class="search-button"><i class="fas fa-search"></i></button>
            </div>
        </form>
    </div>
    <nav class="mobile-nav">
        <ul>
            <li><a href="{% url 'home' %}" {% if request.path == '/' %}class="active"{% endif %}><i class="fas fa-home"></i> Home</a></li>
            <li><a href="{% url 'pet-list' %}" {% if '/pets/' in request.path %}class="active"{% endif %}><i class="fas fa-paw"></i> Pets</a></li>
            <li><a href="{% url 'product-list' %}" {% if '/shop/' in request.path %}class="active"{% endif %}><i class="fas fa-shopping-bag"></i> Shop</a></li>
            <li><a href="{% url 'services-home' %}" {% if '/services/' in request.path %}class="active"{% endif %}><i class="fas fa-concierge-bell"></i> Services</a></li>
            {% if user.is_authenticated %}
                <li><a href="{% url 'user-profile' user_id=user.id %}"><i class="fas fa-user"></i> My Profile</a></li>
                <li><a href="{% url 'my-pets' %}"><i class="fas fa-paw"></i> My Pets</a></li>
                <li><a href="{% url 'pet-create' %}"><i class="fas fa-plus-circle"></i> Add Pet</a></li>
                <li><a href="{% url 'cart' %}"><i class="fas fa-shopping-cart"></i> Cart</a></li>
                <li><a href="{% url 'order-list' %}"><i class="fas fa-box"></i> My Orders</a></li>
                <li><a href="{% url 'booking-list' %}"><i class="fas fa-calendar-check"></i> My Bookings</a></li>
                <li><a href="{% url 'notifications' %}"><i class="fas fa-bell"></i> Notifications</a></li>
                <li><a href="{% url 'inbox' %}"><i class="fas fa-envelope"></i> Messages</a></li>
                {% if user.is_service_provider %}
                <li><a href="{% url 'provider-dashboard' %}"><i class="fas fa-tachometer-alt"></i> Provider Dashboard</a></li>
                {% else %}
                <li><a href="{% url 'become-provider' %}"><i class="fas fa-user-tie"></i> Become a Provider</a></li>
                {% endif %}
                {% if user.is_seller %}
                <li><a href="{% url 'seller-dashboard' %}"><i class="fas fa-store"></i> Seller Dashboard</a></li>
                {% else %}
                <li><a href="{% url 'become-seller' %}"><i class="fas fa-shopping-bag"></i> Become a Seller</a></li>
                {% endif %}
                <li><a href="/users/profile/edit/"><i class="fas fa-cog"></i> Settings</a></li>
                <li><a href="{% url 'account_logout' %}"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
            {% else %}
                <li><a href="{% url 'account_login' %}"><i class="fas fa-sign-in-alt"></i> Login</a></li>
                <li><a href="{% url 'account_signup' %}"><i class="fas fa-user-plus"></i> Sign Up</a></li>
            {% endif %}
        </ul>
    </nav>
</div>
