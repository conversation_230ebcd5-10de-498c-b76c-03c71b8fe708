from allauth.account.adapter import De<PERSON>ult<PERSON>ccountAdapter
from allauth.account.utils import user_email, user_field, user_username
from django.contrib.auth import get_user_model

User = get_user_model()


class CustomAccountAdapter(DefaultAccountAdapter):
    """Custom account adapter for handling user registration"""
    
    def save_user(self, request, user, form, commit=True):
        """
        Saves a new `User` instance using information provided in the
        signup form.
        """
        # Get the data from the form
        data = form.cleaned_data
        
        # Set basic user fields
        user_email(user, data.get('email'))
        user_username(user, data.get('email'))  # Use email as username
        
        # Set custom fields
        if 'full_name' in data:
            user_field(user, 'full_name', data['full_name'])
        if 'phone_number' in data:
            user_field(user, 'phone_number', data['phone_number'])
        
        # Mark profile as completed
        user_field(user, 'profile_completed', True)
        
        # Set password
        if 'password1' in data:
            user.set_password(data['password1'])
        
        # Save the user
        if commit:
            user.save()
        
        return user
    
    def clean_email(self, email):
        """
        Validates an email value. You can hook into this if you want to
        (dynamically) restrict what email addresses can be chosen.
        """
        return email
    
    def clean_username(self, username, shallow=False):
        """
        Validates the username. You can hook into this if you want to
        (dynamically) restrict what usernames can be chosen.
        """
        # Since we're using email as username, just return it
        return username
