{% extends 'base.html' %}
{% load static %}

{% block title %}My Products - Seller Dashboard{% endblock %}

{% block content %}
<div class="container">
    <div class="seller-dashboard">
        <div class="dashboard-sidebar">
            {% include 'components/seller_sidebar.html' %}
        </div>

        <div class="dashboard-main">
            <div class="dashboard-header">
                <h1>My Products</h1>
                <a href="{% url 'add-product' %}" class="btn btn-primary">
                    <i class="fas fa-plus-circle"></i>
                    Add New Product
                </a>
            </div>

            {% if products %}
                <div class="products-grid">
                    {% for product in products %}
                        <div class="product-card">
                            <div class="product-image">
                                <img src="{{ product.image.url }}" alt="{{ product.name }}">
                                {% if not product.is_available %}
                                    <div class="product-overlay">
                                        <span class="status-badge status-inactive">Inactive</span>
                                    </div>
                                {% endif %}
                            </div>

                            <div class="product-content">
                                <div class="product-category">{{ product.category.name }}</div>
                                <h3 class="product-title">{{ product.name }}</h3>
                                <div class="product-price">
                                    {% if product.discount_price %}
                                        <span class="original-price">${{ product.price }}</span>
                                        <span class="current-price">${{ product.discount_price }}</span>
                                    {% else %}
                                        <span class="current-price">${{ product.price }}</span>
                                    {% endif %}
                                </div>
                                <div class="product-stock">
                                    <i class="fas fa-boxes"></i>
                                    Stock: {{ product.stock }}
                                </div>
                            </div>

                            <div class="product-actions">
                                <a href="{% url 'edit-product' pk=product.pk %}" class="btn btn-sm btn-outline">
                                    <i class="fas fa-edit"></i>
                                    Edit
                                </a>
                                <a href="{% url 'delete-product' pk=product.pk %}" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i>
                                    Delete
                                </a>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <i class="fas fa-boxes"></i>
                    <h3>No products yet</h3>
                    <p>Start building your store by adding your first product.</p>
                    <a href="{% url 'add-product' %}" class="btn btn-primary">
                        <i class="fas fa-plus-circle"></i>
                        Add Your First Product
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.seller-dashboard {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: var(--spacing-xl);
    padding: var(--spacing-xl) 0;
}

.dashboard-main {
    min-height: 600px;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-2xl);
}

.dashboard-header h1 {
    color: var(--primary);
    margin: 0;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-base);
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--font-sm);
    font-weight: var(--fw-medium);
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-base);
}

.btn-primary {
    background-color: var(--primary);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-xs);
}

.btn-outline {
    background: transparent;
    border: 1px solid var(--primary);
    color: var(--primary);
}

.btn-outline:hover {
    background: var(--primary);
    color: var(--white);
}

.btn-danger {
    background-color: var(--danger);
    color: var(--white);
}

.btn-danger:hover {
    background-color: var(--danger-dark);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-xl);
}

.product-card {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition-base);
}

.product-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.product-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-content {
    padding: var(--spacing-base);
}

.product-category {
    color: var(--text-muted);
    font-size: var(--font-xs);
    text-transform: uppercase;
    font-weight: var(--fw-medium);
    margin-bottom: var(--spacing-xs);
}

.product-title {
    font-size: var(--font-base);
    font-weight: var(--fw-semibold);
    color: var(--text);
    margin: 0 0 var(--spacing-sm) 0;
    line-height: 1.4;
}

.product-price {
    margin-bottom: var(--spacing-sm);
}

.original-price {
    color: var(--text-muted);
    text-decoration: line-through;
    font-size: var(--font-sm);
    margin-right: var(--spacing-xs);
}

.current-price {
    color: var(--primary);
    font-weight: var(--fw-bold);
    font-size: var(--font-lg);
}

.product-stock {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--text-muted);
    font-size: var(--font-sm);
}

.product-actions {
    padding: var(--spacing-base);
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: var(--spacing-sm);
}

.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: var(--font-xs);
    font-weight: var(--fw-medium);
    text-transform: uppercase;
}

.status-inactive {
    background: var(--warning-light);
    color: var(--warning-dark);
}

.empty-state {
    text-align: center;
    padding: var(--spacing-4xl);
    color: var(--text-muted);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.empty-state h3 {
    color: var(--text);
    margin-bottom: var(--spacing-base);
}

.empty-state p {
    margin-bottom: var(--spacing-xl);
    font-size: var(--font-lg);
}

@media (max-width: 992px) {
    .seller-dashboard {
        grid-template-columns: 1fr;
    }
    
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-base);
    }
}

@media (max-width: 768px) {
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
    
    .product-actions {
        flex-direction: column;
    }
}
</style>
{% endblock %}
