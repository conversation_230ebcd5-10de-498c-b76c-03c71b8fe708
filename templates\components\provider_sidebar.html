<!-- Service Provider Sidebar Component -->
<div class="provider-sidebar">
    <div class="provider-profile-card">
        {% if user.service_provider.profile_picture %}
            <img src="{{ user.service_provider.profile_picture.url }}" 
                 alt="{{ user.full_name|default:user.email }}" 
                 class="provider-avatar">
        {% else %}
            <img src="/static/img/default-profile.png" 
                 alt="{{ user.full_name|default:user.email }}" 
                 class="provider-avatar">
        {% endif %}
        <h5 class="provider-name">{{ user.full_name|default:user.email }}</h5>
        <p class="provider-role">Service Provider</p>
        <div class="d-grid gap-2">
            <a href="{% url 'provider-detail' user.service_provider.id %}" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-eye"></i> View Public Profile
            </a>
        </div>
    </div>
    
    <nav class="provider-nav">
        <ul class="provider-nav">
            <li class="provider-nav-item">
                <a href="{% url 'provider-dashboard' %}" 
                   class="provider-nav-link {% if active_tab == 'dashboard' %}active{% endif %}">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </a>
            </li>
            <li class="provider-nav-item">
                <a href="{% url 'provider-services' %}" 
                   class="provider-nav-link {% if active_tab == 'services' %}active{% endif %}">
                    <i class="fas fa-concierge-bell"></i>
                    My Services
                </a>
            </li>
            <li class="provider-nav-item">
                <a href="{% url 'provider-bookings' %}" 
                   class="provider-nav-link {% if active_tab == 'bookings' %}active{% endif %}">
                    <i class="fas fa-calendar-check"></i>
                    Bookings
                </a>
            </li>
            <li class="provider-nav-item">
                <a href="{% url 'provider-availability' %}" 
                   class="provider-nav-link {% if active_tab == 'availability' %}active{% endif %}">
                    <i class="fas fa-clock"></i>
                    Availability
                </a>
            </li>
            <li class="provider-nav-item">
                <a href="{% url 'provider-settings' %}" 
                   class="provider-nav-link {% if active_tab == 'settings' %}active{% endif %}">
                    <i class="fas fa-cog"></i>
                    Settings
                </a>
            </li>
        </ul>
    </nav>
</div>
