{% extends 'base.html' %}
{% load static %}

{% block title %}Delete Product - Seller Dashboard{% endblock %}

{% block content %}
<div class="container">
    <div class="seller-dashboard">
        <div class="dashboard-sidebar">
            {% include 'components/seller_sidebar.html' %}
        </div>

        <div class="dashboard-main">
            <div class="delete-confirmation">
                <div class="confirmation-header">
                    <h1>Delete Product</h1>
                    <p>Are you sure you want to delete this product? This action cannot be undone.</p>
                </div>

                <div class="product-preview">
                    <div class="product-image">
                        <img src="{{ product.image.url }}" alt="{{ product.name }}">
                    </div>
                    <div class="product-details">
                        <h3>{{ product.name }}</h3>
                        <div class="product-meta">
                            <div class="meta-item">
                                <span class="label">Category:</span>
                                <span class="value">{{ product.category.name }}</span>
                            </div>
                            {% if product.pet_category %}
                            <div class="meta-item">
                                <span class="label">Pet Category:</span>
                                <span class="value">{{ product.pet_category.name }}</span>
                            </div>
                            {% endif %}
                            <div class="meta-item">
                                <span class="label">Price:</span>
                                <span class="value">${{ product.get_current_price }}</span>
                            </div>
                            <div class="meta-item">
                                <span class="label">Stock:</span>
                                <span class="value">{{ product.stock }} units</span>
                            </div>
                            <div class="meta-item">
                                <span class="label">Status:</span>
                                <span class="value">
                                    {% if product.is_available %}
                                        <span class="status-badge status-active">Active</span>
                                    {% else %}
                                        <span class="status-badge status-inactive">Inactive</span>
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="warning-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div>
                        <h4>Warning</h4>
                        <p>Deleting this product will:</p>
                        <ul>
                            <li>Permanently remove the product from your store</li>
                            <li>Remove it from all customer wishlists</li>
                            <li>Make it unavailable for future purchases</li>
                            <li>This action cannot be undone</li>
                        </ul>
                    </div>
                </div>

                <div class="confirmation-actions">
                    <a href="{% url 'seller-products' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        Cancel
                    </a>
                    <form method="POST" style="display: inline;">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i>
                            Delete Product
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.seller-dashboard {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: var(--spacing-xl);
    padding: var(--spacing-xl) 0;
}

.delete-confirmation {
    max-width: 600px;
}

.confirmation-header {
    margin-bottom: var(--spacing-2xl);
}

.confirmation-header h1 {
    color: var(--danger);
    margin-bottom: var(--spacing-xs);
}

.confirmation-header p {
    color: var(--text-muted);
    font-size: var(--font-lg);
}

.product-preview {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    display: flex;
    gap: var(--spacing-lg);
}

.product-image {
    flex-shrink: 0;
}

.product-image img {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: var(--border-radius);
    border: 2px solid var(--border-color);
}

.product-details {
    flex: 1;
}

.product-details h3 {
    color: var(--text);
    margin-bottom: var(--spacing-base);
    font-size: var(--font-xl);
}

.product-meta {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.meta-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.meta-item .label {
    font-weight: var(--fw-medium);
    color: var(--text-muted);
    min-width: 100px;
}

.meta-item .value {
    color: var(--text);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: var(--font-xs);
    font-weight: var(--fw-medium);
    text-transform: uppercase;
}

.status-active {
    background: var(--success-light);
    color: var(--success-dark);
}

.status-inactive {
    background: var(--warning-light);
    color: var(--warning-dark);
}

.warning-message {
    background: var(--warning-light);
    border: 1px solid var(--warning);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    display: flex;
    gap: var(--spacing-base);
    align-items: flex-start;
}

.warning-message i {
    color: var(--warning);
    font-size: var(--font-xl);
    margin-top: var(--spacing-xs);
}

.warning-message h4 {
    color: var(--warning-dark);
    margin: 0 0 var(--spacing-sm) 0;
}

.warning-message p {
    color: var(--warning-dark);
    margin: 0 0 var(--spacing-sm) 0;
}

.warning-message ul {
    color: var(--warning-dark);
    margin: 0;
    padding-left: var(--spacing-lg);
}

.warning-message li {
    margin-bottom: var(--spacing-xs);
}

.confirmation-actions {
    display: flex;
    gap: var(--spacing-base);
    justify-content: flex-end;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--font-base);
    font-weight: var(--fw-medium);
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-base);
}

.btn-secondary {
    background-color: var(--gray-200);
    color: var(--text);
}

.btn-secondary:hover {
    background-color: var(--gray-300);
}

.btn-danger {
    background-color: var(--danger);
    color: var(--white);
}

.btn-danger:hover {
    background-color: var(--danger-dark);
    transform: translateY(-1px);
}

@media (max-width: 992px) {
    .seller-dashboard {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .product-preview {
        flex-direction: column;
        text-align: center;
    }
    
    .product-image {
        align-self: center;
    }
    
    .confirmation-actions {
        flex-direction: column;
    }
    
    .warning-message {
        flex-direction: column;
        text-align: center;
    }
}
</style>
{% endblock %}
