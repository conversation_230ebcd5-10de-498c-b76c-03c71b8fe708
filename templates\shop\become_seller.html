{% extends 'base.html' %}
{% load static %}

{% block title %}Become a Seller - PetPaw{% endblock %}

{% block content %}
<div class="container">
    <div class="seller-registration-container">
        <div class="seller-registration-header">
            <h1>Become a Seller</h1>
            <p>Join PetPaw marketplace and start selling your pet products to thousands of pet lovers!</p>
        </div>

        <div class="seller-registration-form">
            <form method="POST" enctype="multipart/form-data">
                {% csrf_token %}

                <div class="form-section">
                    <h3><i class="fas fa-store"></i> Business Information</h3>
                    
                    <div class="form-group">
                        <label for="{{ form.business_name.id_for_label }}">Business Name *</label>
                        {{ form.business_name }}
                        {% if form.business_name.errors %}
                            <div class="error-message">{{ form.business_name.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        <label for="{{ form.business_description.id_for_label }}">Business Description *</label>
                        {{ form.business_description }}
                        {% if form.business_description.errors %}
                            <div class="error-message">{{ form.business_description.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        <label for="{{ form.business_address.id_for_label }}">Business Address *</label>
                        {{ form.business_address }}
                        {% if form.business_address.errors %}
                            <div class="error-message">{{ form.business_address.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>

                <div class="form-section">
                    <h3><i class="fas fa-phone"></i> Contact Information</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.business_phone.id_for_label }}">Business Phone *</label>
                            {{ form.business_phone }}
                            {% if form.business_phone.errors %}
                                <div class="error-message">{{ form.business_phone.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="{{ form.business_email.id_for_label }}">Business Email *</label>
                            {{ form.business_email }}
                            {% if form.business_email.errors %}
                                <div class="error-message">{{ form.business_email.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="{{ form.business_website.id_for_label }}">Business Website</label>
                        {{ form.business_website }}
                        {% if form.business_website.errors %}
                            <div class="error-message">{{ form.business_website.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        <label for="{{ form.business_logo.id_for_label }}">Business Logo</label>
                        {{ form.business_logo }}
                        {% if form.business_logo.errors %}
                            <div class="error-message">{{ form.business_logo.errors.0 }}</div>
                        {% endif %}
                        <small class="form-help">Upload your business logo (optional)</small>
                    </div>
                </div>

                <div class="form-actions">
                    <a href="{% url 'product-list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-store"></i>
                        Become a Seller
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.seller-registration-container {
    max-width: 800px;
    margin: 0 auto;
    padding: var(--spacing-xl) 0;
}

.seller-registration-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.seller-registration-header h1 {
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
}

.seller-registration-header p {
    color: var(--text-muted);
    font-size: var(--font-lg);
}

.seller-registration-form {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-sm);
}

.form-section {
    margin-bottom: var(--spacing-2xl);
}

.form-section h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-light);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: var(--fw-medium);
    color: var(--text);
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: var(--font-base);
    transition: var(--transition-base);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.form-help {
    display: block;
    margin-top: var(--spacing-xs);
    color: var(--text-muted);
    font-size: var(--font-sm);
}

.error-message {
    color: var(--danger);
    font-size: var(--font-sm);
    margin-top: var(--spacing-xs);
}

.form-actions {
    display: flex;
    gap: var(--spacing-base);
    justify-content: flex-end;
    margin-top: var(--spacing-2xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--font-base);
    font-weight: var(--fw-medium);
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-base);
}

.btn-primary {
    background-color: var(--primary);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: var(--gray-200);
    color: var(--text);
}

.btn-secondary:hover {
    background-color: var(--gray-300);
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .seller-registration-form {
        padding: var(--spacing-lg);
    }
}
</style>
{% endblock %}
